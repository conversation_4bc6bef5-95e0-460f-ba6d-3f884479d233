-- 合并数据库设置文件（完全修复版）
-- 创建时间: 2025-01-14
-- 修复时间: 2025-01-14

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ====================================
-- 表结构
-- ====================================

DROP TABLE IF EXISTS `admin_users`;
CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_login` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员用户表';

DROP TABLE IF EXISTS `system_settings`;
CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统设置表';

DROP TABLE IF EXISTS `smtp_config`;
CREATE TABLE `smtp_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `smtp_host` varchar(255) NOT NULL,
  `smtp_port` int(11) NOT NULL DEFAULT 465,
  `smtp_username` varchar(255) NOT NULL,
  `smtp_password` varchar(255) NOT NULL,
  `from_email` varchar(255) DEFAULT NULL,
  `from_name` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_smtp` (`user_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SMTP配置表';

DROP TABLE IF EXISTS `trusted_devices`;
CREATE TABLE `trusted_devices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `device_name` varchar(255) NOT NULL,
  `device_fingerprint` varchar(255) NOT NULL,
  `user_agent` text,
  `ip_address` varchar(45) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_device` (`user_id`, `device_fingerprint`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_fingerprint` (`device_fingerprint`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='信任设备表';

DROP TABLE IF EXISTS `verification_codes`;
CREATE TABLE `verification_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `code` varchar(6) NOT NULL,
  `type` varchar(50) NOT NULL,
  `expires_at` timestamp NOT NULL,
  `is_used` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_code` (`user_id`, `code`),
  KEY `idx_expires` (`expires_at`),
  KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='验证码表';

DROP TABLE IF EXISTS `login_logs`;
CREATE TABLE `login_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `device_info` text,
  `device_fingerprint` varchar(255) DEFAULT NULL,
  `login_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `login_status` enum('success','failed','blocked') NOT NULL DEFAULT 'success',
  `is_trusted_device` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_login_time` (`login_time`),
  KEY `idx_status` (`login_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录日志表';

DROP TABLE IF EXISTS `license_keys`;
CREATE TABLE `license_keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_value` varchar(255) NOT NULL COMMENT '卡密值',
  `license_key` varchar(255) DEFAULT NULL COMMENT '卡密值（兼容字段）',
  `card_number` varchar(50) DEFAULT NULL COMMENT '卡号（兼容字段）',
  `type` enum('hour','day','month','year','hourly','daily','monthly','half_yearly','yearly') NOT NULL DEFAULT 'day',
  `card_type` enum('hour','day','month','year') DEFAULT NULL COMMENT '卡密类型（兼容字段）',
  `duration` int(11) NOT NULL DEFAULT 1,
  `status` enum('unused','used','expired','active','banned','disabled') NOT NULL DEFAULT 'unused',
  `used_by` varchar(100) DEFAULT NULL,
  `used_at` timestamp NULL DEFAULT NULL,
  `last_heartbeat` timestamp NULL DEFAULT NULL,
  `last_used_ip` varchar(45) DEFAULT NULL,
  `script_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `store_name` varchar(200) DEFAULT NULL COMMENT '绑定的店铺名称',
  `wechat_store_id` varchar(100) DEFAULT NULL COMMENT '绑定的微信小店ID',
  `user_wechat` varchar(100) DEFAULT NULL COMMENT '用户微信号',
  `phone_number` varchar(20) DEFAULT NULL COMMENT '绑定的手机号码',

  `has_customer_service` tinyint(1) NOT NULL DEFAULT 1 COMMENT '小梅花AI客服-微信小店',
  `has_product_listing` tinyint(1) NOT NULL DEFAULT 0 COMMENT '小梅花AI客服-抖店',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NULL DEFAULT NULL,
  `expiry_date` timestamp NULL DEFAULT NULL COMMENT '过期日期（兼容字段）',
  `last_used_at` datetime DEFAULT NULL,
  `last_checked_at` datetime DEFAULT NULL,
  `last_heartbeat_at` datetime DEFAULT NULL,
  `last_ip` varchar(45) DEFAULT NULL,
  `last_user_agent` varchar(255) DEFAULT NULL,
  `store_url` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key_value` (`key_value`),
  UNIQUE KEY `card_number` (`card_number`),
  UNIQUE KEY `license_key` (`license_key`),
  KEY `idx_status` (`status`),
  KEY `idx_type` (`type`),
  KEY `idx_card_type` (`card_type`),
  KEY `idx_script_id` (`script_id`),
  KEY `idx_store_info` (`store_name`, `wechat_store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卡密表';

DROP TABLE IF EXISTS `scripts`;
CREATE TABLE `scripts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `content` longtext DEFAULT NULL COMMENT '脚本内容（兼容字段）',
  `script_code` longtext NOT NULL COMMENT '脚本代码',
  `loader_code` longtext DEFAULT NULL COMMENT '加载器代码',
  `version` varchar(50) NOT NULL DEFAULT '1.0.0',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活（兼容字段）',
  `default_ai_enabled` tinyint(1) DEFAULT 0 COMMENT '默认是否启用AI',
  `url_patterns` text DEFAULT NULL COMMENT 'URL匹配规则，JSON格式存储多个URL',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_status` (`status`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='脚本表';

DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `status` enum('online','offline') NOT NULL DEFAULT 'offline',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_login` timestamp NULL DEFAULT NULL,
  `key_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `idx_status` (`status`),
  KEY `idx_key_id` (`key_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';



DROP TABLE IF EXISTS `api_logs`;
CREATE TABLE `api_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `endpoint` varchar(100) NOT NULL,
  `method` varchar(10) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text,
  `request_data` text,
  `response_data` text,
  `response_status` varchar(20) DEFAULT NULL,
  `message` text DEFAULT NULL,
  `status_code` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_endpoint` (`endpoint`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_status_code` (`status_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API日志表';

DROP TABLE IF EXISTS `api_tokens`;
CREATE TABLE `api_tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `token` varchar(255) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `expires_at` datetime NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `last_used_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `token` (`token`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `dynamic_endpoints`;
CREATE TABLE `dynamic_endpoints` (
  `endpoint_id` int(11) NOT NULL AUTO_INCREMENT,
  `endpoint_name` varchar(50) NOT NULL,
  `key_id` int(11) NOT NULL,
  `handler_function` varchar(50) NOT NULL,
  `require_auth` tinyint(1) DEFAULT 1,
  `is_active` tinyint(1) DEFAULT 1,
  `use_count` int(11) DEFAULT 0,
  `expires_at` datetime NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `last_used_at` datetime DEFAULT NULL,
  PRIMARY KEY (`endpoint_id`),
  UNIQUE KEY `endpoint_name` (`endpoint_name`),
  KEY `key_id` (`key_id`),
  KEY `expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `heartbeat_logs`;
CREATE TABLE `heartbeat_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `client_info` text DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `key_id` (`key_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `key_usage_logs`;
CREATE TABLE `key_usage_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `shop_name` varchar(100) DEFAULT NULL,
  `shop_url` varchar(255) DEFAULT NULL,
  `client_info` text DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `key_id` (`key_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `key_status_checks`;
CREATE TABLE `key_status_checks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `client_info` text DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `key_id` (`key_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `api_security_config`;
CREATE TABLE `api_security_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(50) NOT NULL,
  `config_value` text NOT NULL,
  `description` text DEFAULT NULL,
  `is_encrypted` tinyint(1) DEFAULT 0,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `api_blacklist`;
CREATE TABLE `api_blacklist` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fingerprint` varchar(32) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `reason` enum('BRUTE_FORCE','GEO_ANOMALY','MULTIPLE_FAILURES','ADMIN_BAN') NOT NULL,
  `expires_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `fingerprint` (`fingerprint`),
  KEY `ip_address` (`ip_address`),
  KEY `expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `api_key_sync`;
CREATE TABLE `api_key_sync` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_id` int(11) NOT NULL,
  `sync_status` enum('PENDING','SYNCED','FAILED') NOT NULL DEFAULT 'PENDING',
  `last_sync_at` datetime DEFAULT NULL,
  `sync_attempts` int(11) DEFAULT 0,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key_id` (`key_id`),
  KEY `sync_status` (`sync_status`),
  KEY `last_sync_at` (`last_sync_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

DROP TABLE IF EXISTS `api_key_cache`;
CREATE TABLE `api_key_cache` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_value` varchar(255) NOT NULL,
  `key_data` text NOT NULL,
  `expires_at` datetime NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key_value` (`key_value`),
  KEY `expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ====================================
-- 数据插入
-- ====================================

-- 插入默认管理员用户（用户名：admin，密码：123456）
INSERT INTO `admin_users` (`username`, `password`, `email`) VALUES 
('admin', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>');

-- 插入默认API配置
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `description`) VALUES
('api_version', 'v2', 'API版本'),
('api_secret_key', MD5(RAND()), 'API密钥，用于签名验证'),
('api_enable_signature_validation', 'true', '是否启用签名验证'),
('api_enable_timestamp_validation', 'true', '是否启用时间戳验证'),
('api_enable_token_validation', 'true', '是否启用令牌验证'),
('api_timestamp_ttl', '300', '时间戳有效期（秒）'),
('api_token_ttl', '3600', '令牌有效期（秒）'),
('api_debug_mode', 'false', '是否启用调试模式'),
('api_log_requests', 'true', '是否记录API请求'),
('api_rate_limit_enabled', 'true', '是否启用速率限制'),
('api_rate_limit_requests', '60', '每分钟最大请求数'),
('api_rate_limit_window', '60', '速率限制窗口（秒）'),
('api_encryption_enabled', 'true', '是否启用加密'),
('api_encryption_algorithm', 'aes-256-gcm', '加密算法'),
('api_dynamic_endpoint_enabled', 'true', '是否启用动态端点')
ON DUPLICATE KEY UPDATE `setting_value` = VALUES(`setting_value`), `description` = VALUES(`description`);

-- 插入默认API安全配置
INSERT INTO `api_security_config` (`config_key`, `config_value`, `description`, `is_encrypted`) VALUES
('primary_server', 'https://xiaomeihuakefu.cn', '主服务器地址', 0),
('backup_server', 'https://api.xiaomeihuakefu.cn', '备用服务器地址', 0),
('secure_server', 'https://secure.xiaomeihuakefu.cn', '安全服务器地址', 0),
('encryption_key', MD5(RAND()), '数据加密密钥', 1),
('signature_salt', MD5(RAND()), '签名盐值', 1),
('token_secret', MD5(RAND()), '令牌密钥', 1),
('whitelist_ips', '[]', '白名单IP列表（JSON数组）', 0),
('blacklist_ips', '[]', '黑名单IP列表（JSON数组）', 0),
('max_failed_attempts', '5', '最大失败尝试次数', 0),
('lockout_time', '1800', '锁定时间（秒）', 0),
('secure_headers', '{"X-Frame-Options":"DENY","X-XSS-Protection":"1; mode=block","X-Content-Type-Options":"nosniff"}', '安全HTTP头（JSON对象）', 0)
ON DUPLICATE KEY UPDATE `config_value` = VALUES(`config_value`), `description` = VALUES(`description`), `is_encrypted` = VALUES(`is_encrypted`);

SET FOREIGN_KEY_CHECKS = 1; 