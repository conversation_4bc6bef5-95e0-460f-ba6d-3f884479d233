-- 数据库升级脚本：添加手机号码绑定支持
-- 版本：v1.0
-- 日期：2025-08-21
-- 功能：为卡密系统添加手机号码绑定功能

-- 检查并添加手机号码字段
SET @sql = '';
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'license_keys' 
  AND COLUMN_NAME = 'phone_number';

-- 如果字段不存在，则添加
SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE license_keys ADD COLUMN phone_number varchar(20) DEFAULT NULL COMMENT ''绑定的手机号码'' AFTER user_wechat',
    'SELECT ''phone_number字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加手机号码索引（如果不存在）
SET @sql = '';
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'license_keys' 
  AND INDEX_NAME = 'idx_phone_number';

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE license_keys ADD INDEX idx_phone_number (phone_number)',
    'SELECT ''idx_phone_number索引已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 创建手机号码登录日志表（如果不存在）
CREATE TABLE IF NOT EXISTS `phone_login_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `phone_number` varchar(20) NOT NULL COMMENT '手机号码',
  `license_key_id` int(11) NOT NULL COMMENT '关联的卡密ID',
  `login_ip` varchar(45) DEFAULT NULL COMMENT '登录IP',
  `login_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  `login_status` enum('success','failed','blocked') NOT NULL DEFAULT 'success' COMMENT '登录状态',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '用户代理',
  PRIMARY KEY (`id`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_license_key_id` (`license_key_id`),
  KEY `idx_login_time` (`login_time`),
  KEY `idx_login_status` (`login_status`),
  FOREIGN KEY (`license_key_id`) REFERENCES `license_keys`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='手机号码登录日志表';

-- 输出升级完成信息
SELECT 'phone_number字段和相关索引已成功添加到license_keys表' as upgrade_status;
SELECT 'phone_login_logs表已创建' as table_status;
