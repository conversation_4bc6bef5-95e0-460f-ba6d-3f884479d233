<?php
// 获取flash消息函数
function getFlashMessage() {
    $messages = [];
    
    if (isset($_SESSION['flash_success'])) {
        $messages['success'] = $_SESSION['flash_success'];
        unset($_SESSION['flash_success']);
    }
    
    if (isset($_SESSION['flash_error'])) {
        $messages['error'] = $_SESSION['flash_error'];
        unset($_SESSION['flash_error']);
    }
    
    return $messages;
}

// POST重定向函数
function handlePostRedirect($page, $success_message = '', $error_message = '', $generated_keys = []) {
    // 将消息存储到session中
    if (!empty($success_message)) {
        $_SESSION['flash_success'] = $success_message;
    }
    if (!empty($error_message)) {
        $_SESSION['flash_error'] = $error_message;
    }
    if (!empty($generated_keys)) {
        $_SESSION['generated_keys'] = $generated_keys;
    }
    
    // 重定向到指定页面
    $redirect_url = "index.php?page=" . urlencode($page);
    header("Location: " . $redirect_url);
    exit;
}

// 获取flash消息
$flash_messages = getFlashMessage();
$success_message = $flash_messages['success'] ?? '';
$error_message = $flash_messages['error'] ?? '';

// 获取生成的卡密
$generated_key = '';
if (isset($_SESSION['generated_keys'])) {
    $generated_key = implode('<br>', $_SESSION['generated_keys']);
    unset($_SESSION['generated_keys']);
}

$message = '';
if ($success_message) {
    $message = "<div class='message success'><i class='fas fa-check-circle'></i> {$success_message}</div>";
}
if ($error_message) {
    $message = "<div class='message error'><i class='fas fa-times-circle'></i> {$error_message}</div>";
}

// 获取所有脚本用于下拉选择
$scripts_stmt = $pdo->query("SELECT id, name, version FROM scripts ORDER BY created_at DESC");
$scripts = $scripts_stmt->fetchAll(PDO::FETCH_ASSOC);

// 生成卡密
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['generate_key'])) {
    $store_name = !empty($_POST['store_name']) ? trim($_POST['store_name']) : null;
    $wechat_store_id = !empty($_POST['wechat_store_id']) ? trim($_POST['wechat_store_id']) : null;

    $type = $_POST['type'];
    $quantity = intval($_POST['quantity'] ?? 1);
    
    // 功能权限设置 - 修复：检查值而不是是否存在
    $has_customer_service = (isset($_POST['has_customer_service']) && $_POST['has_customer_service'] == '1') ? 1 : 0;
    $has_product_listing = (isset($_POST['has_product_listing']) && $_POST['has_product_listing'] == '1') ? 1 : 0;

    // 验证卡密功能必须选择
    if (!$has_customer_service && !$has_product_listing) {
        $message = "<div class='message error'><i class='fas fa-exclamation-triangle'></i> 必须选择至少一个卡密功能</div>";
    } elseif ($quantity < 1 || $quantity > 100) {
        $message = "<div class='message error'><i class='fas fa-exclamation-triangle'></i> 生成数量必须在1-100之间</div>";
    } else {
        $expiry_interval = '';
        switch ($type) {
            case 'yearly': $expiry_interval = '+1 year'; break;
            case 'half_yearly': $expiry_interval = '+6 months'; break;
            case 'monthly': $expiry_interval = '+1 month'; break;
            case 'daily': $expiry_interval = '+1 day'; break;
            case 'hourly': $expiry_interval = '+1 hour'; break;
        }

        if ($expiry_interval) {
            $generated_keys = [];
            $errors = [];

            for ($i = 0; $i < $quantity; $i++) {
                try {
                    $new_key = generate_key();
                    $expiry_date = (new DateTime())->modify($expiry_interval)->format('Y-m-d H:i:s');

                    // 验证生成的卡密
                    if (empty($new_key)) {
                        $error_msg = "生成的卡密为空";
                        $errors[] = $error_msg;
                        error_log($error_msg);
                        continue;
                    }

                    // 检查卡密是否已存在
                    $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM license_keys WHERE key_value = ?");
                    $check_stmt->execute([$new_key]);
                    if ($check_stmt->fetchColumn() > 0) {
                        // 如果卡密已存在，重新生成
                        $i--;
                        continue;
                    }

                    // 验证必要参数
                    if (empty($type)) {
                        $error_msg = "卡密类型不能为空";
                        $errors[] = $error_msg;
                        error_log($error_msg);
                        continue;
                    }

                    $stmt = $pdo->prepare(
                        "INSERT INTO license_keys (key_value, type, store_name, wechat_store_id, expiry_date, status, has_customer_service, has_product_listing, created_at)
                         VALUES (?, ?, ?, ?, ?, 'active', ?, ?, NOW())"
                    );

                    $execute_result = $stmt->execute([$new_key, $type, $store_name, $wechat_store_id, $expiry_date, $has_customer_service, $has_product_listing]);

                    if ($execute_result) {
                        $generated_keys[] = $new_key;
                        error_log("成功生成卡密: $new_key (类型: $type, 过期时间: $expiry_date)");
                    } else {
                        $error_info = $stmt->errorInfo();
                        $error_msg = "卡密插入失败 - SQLSTATE: " . $error_info[0] . ", 错误代码: " . $error_info[1] . ", 错误信息: " . $error_info[2];
                        $errors[] = $error_msg;
                        error_log($error_msg);
                        error_log("插入参数: key=$new_key, type=$type, store_name=$store_name, wechat_store_id=$wechat_store_id, expiry_date=$expiry_date, has_customer_service=$has_customer_service, has_product_listing=$has_product_listing");
                    }
                } catch (Exception $e) {
                    $error_msg = "生成卡密异常: " . $e->getMessage() . " (文件: " . $e->getFile() . ", 行: " . $e->getLine() . ")";
                    $errors[] = $error_msg;
                    error_log($error_msg);
                }
            }

            if (!empty($generated_keys)) {
                handlePostRedirect('keys', '成功生成 ' . count($generated_keys) . ' 个卡密', '', $generated_keys);
            } else {
                $error_detail = !empty($errors) ? ' (' . implode(', ', $errors) . ')' : '';
                handlePostRedirect('keys', '', '生成卡密失败' . $error_detail);
            }
        }
    }
}

// 删除卡密
if (isset($_GET['delete_key'])) {
    $key_id = $_GET['delete_key'];
    $stmt = $pdo->prepare("DELETE FROM license_keys WHERE id = ?");
    if ($stmt->execute([$key_id])) {
        handlePostRedirect('keys', '卡密已删除！');
    } else {
        handlePostRedirect('keys', '', '删除失败');
    }
}

// 禁用/启用卡密
if (isset($_GET['toggle_status'])) {
    $key_id = $_GET['toggle_status'];
    $new_status = $_GET['status'] === 'active' ? 'banned' : 'active';
    $stmt = $pdo->prepare("UPDATE license_keys SET status = ? WHERE id = ?");
    if ($stmt->execute([$new_status, $key_id])) {
        $status_text = $new_status === 'active' ? '启用' : '禁用';
        handlePostRedirect('keys', "卡密已{$status_text}！");
    } else {
        handlePostRedirect('keys', '', '操作失败');
    }
}

// 编辑卡密
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['edit_key'])) {
    $key_id = $_POST['key_id'];
    $new_key_value = trim($_POST['key_value']);

    // 处理日期设置逻辑
    $new_expiry_date = '';
    $new_type = null; // 新增：用于存储升级后的卡密类型

    // 优先处理升级选项
    if (!empty($_POST['upgrade_type'])) {
        // 获取当前到期时间
        $current_stmt = $pdo->prepare("SELECT expiry_date FROM license_keys WHERE id = ?");
        $current_stmt->execute([$key_id]);
        $current_expiry = $current_stmt->fetchColumn();

        if ($current_expiry) {
            $current_date = new DateTime($current_expiry);

            switch ($_POST['upgrade_type']) {
                case 'yearly':
                    $current_date->modify('+1 year');
                    $new_type = 'yearly'; // 设置新的卡密类型
                    break;
                case 'half_yearly':
                    $current_date->modify('+6 months');
                    $new_type = 'half_yearly'; // 设置新的卡密类型
                    break;
                case 'monthly':
                    $current_date->modify('+1 month');
                    $new_type = 'monthly'; // 设置新的卡密类型
                    break;
                case 'daily':
                    $current_date->modify('+1 day');
                    $new_type = 'daily'; // 设置新的卡密类型
                    break;
            }

            $new_expiry_date = $current_date->format('Y-m-d H:i:s');
        }
    }
    // 处理手动输入的日期格式 (2025-8-20-00:00)
    elseif (!empty($_POST['manual_expiry_date'])) {
        $manual_date = trim($_POST['manual_expiry_date']);
        if (preg_match('/^(\d{4})-(\d{1,2})-(\d{1,2})-(\d{2}):(\d{2})$/', $manual_date, $matches)) {
            $year = $matches[1];
            $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
            $day = str_pad($matches[3], 2, '0', STR_PAD_LEFT);
            $hour = $matches[4];
            $minute = $matches[5];

            $new_expiry_date = "{$year}-{$month}-{$day} {$hour}:{$minute}:00";
        }
    }
    // 使用标准日期选择器的值
    elseif (!empty($_POST['expiry_date'])) {
        $new_expiry_date = $_POST['expiry_date'];
    }

    $store_name = !empty($_POST['store_name']) ? trim($_POST['store_name']) : null;
    $wechat_store_id = !empty($_POST['wechat_store_id']) ? trim($_POST['wechat_store_id']) : null;
    
    // 功能权限设置 - 修复：检查值而不是是否存在
    $has_customer_service = (isset($_POST['has_customer_service']) && $_POST['has_customer_service'] == '1') ? 1 : 0;
    $has_product_listing = (isset($_POST['has_product_listing']) && $_POST['has_product_listing'] == '1') ? 1 : 0;

    // 手机号码处理
    $phone_number = !empty($_POST['phone_number']) ? trim($_POST['phone_number']) : null;

    // 验证手机号码格式（如果提供了）
    if ($phone_number && !preg_match('/^1[3-9]\d{9}$/', $phone_number)) {
        handlePostRedirect('keys', '', '手机号码格式不正确');
        return;
    }

    // 检查手机号码是否已被其他卡密绑定
    if ($phone_number) {
        $phone_check_stmt = $pdo->prepare("SELECT id FROM license_keys WHERE phone_number = ? AND id != ?");
        $phone_check_stmt->execute([$phone_number, $key_id]);
        if ($phone_check_stmt->fetchColumn()) {
            handlePostRedirect('keys', '', '该手机号码已被其他卡密绑定');
            return;
        }
    }

    // 验证卡密功能必须选择
    if (!$has_customer_service && !$has_product_listing) {
        handlePostRedirect('keys', '', '必须选择至少一个卡密功能');
        return;
    }

    if (!empty($new_key_value) && !empty($new_expiry_date)) {
        // 检查新卡密是否已存在
        $check_stmt = $pdo->prepare("SELECT id FROM license_keys WHERE key_value = ? AND id != ?");
        $check_stmt->execute([$new_key_value, $key_id]);
        
        if ($check_stmt->fetchColumn()) {
            handlePostRedirect('keys', '', '卡密已存在，请使用其他卡密');
        } else {
            // 如果有新类型则同时更新类型
            if ($new_type) {
                $stmt = $pdo->prepare("UPDATE license_keys SET key_value = ?, type = ?, expiry_date = ?, store_name = ?, wechat_store_id = ?, phone_number = ?, has_customer_service = ?, has_product_listing = ? WHERE id = ?");
                $execute_params = [$new_key_value, $new_type, $new_expiry_date, $store_name, $wechat_store_id, $phone_number, $has_customer_service, $has_product_listing, $key_id];
            } else {
                $stmt = $pdo->prepare("UPDATE license_keys SET key_value = ?, expiry_date = ?, store_name = ?, wechat_store_id = ?, phone_number = ?, has_customer_service = ?, has_product_listing = ? WHERE id = ?");
                $execute_params = [$new_key_value, $new_expiry_date, $store_name, $wechat_store_id, $phone_number, $has_customer_service, $has_product_listing, $key_id];
            }

            if ($stmt->execute($execute_params)) {
                handlePostRedirect('keys', '卡密信息已更新！');
            } else {
                handlePostRedirect('keys', '', '更新失败');
            }
        }
    } else {
        handlePostRedirect('keys', '', '卡密和到期时间不能为空');
    }
}

// 获取要编辑的卡密
$edit_key = null;
if (isset($_GET['edit_key'])) {
    $stmt = $pdo->prepare("SELECT * FROM license_keys WHERE id = ?");
    $stmt->execute([$_GET['edit_key']]);
    $edit_key = $stmt->fetch(PDO::FETCH_ASSOC);
}

// 批量删除过期卡密
if (isset($_POST['delete_expired'])) {
    $stmt = $pdo->prepare("DELETE FROM license_keys WHERE status = 'expired' OR expiry_date <= NOW()");
    $deleted_count = $stmt->execute() ? $stmt->rowCount() : 0;
    handlePostRedirect('keys', "已删除 {$deleted_count} 个过期卡密");
}

// 搜索和分页
$search = $_GET['search'] ?? '';
$page_num = max(1, intval($_GET['p'] ?? 1));
$per_page = 20;
$offset = ($page_num - 1) * $per_page;

$where_clause = "";
$params = [];
if (!empty($search)) {
    $where_clause = "WHERE lk.key_value LIKE ? OR lk.store_name LIKE ? OR lk.wechat_store_id LIKE ?";
    $search_param = "%{$search}%";
    $params = [$search_param, $search_param, $search_param];
}

// 获取总数
$count_sql = "SELECT COUNT(*) FROM license_keys lk " . $where_clause;
$count_stmt = $pdo->prepare($count_sql);
$count_stmt->execute($params);
$total_keys = $count_stmt->fetchColumn();
$total_pages = ceil($total_keys / $per_page);

// 获取卡密列表
$keys_sql = "
    SELECT lk.*, s.name as script_name, s.version as script_version 
    FROM license_keys lk 
    LEFT JOIN scripts s ON lk.script_id = s.id 
    {$where_clause}
    ORDER BY lk.id DESC 
    LIMIT {$per_page} OFFSET {$offset}
";
$keys_stmt = $pdo->prepare($keys_sql);
$keys_stmt->execute($params);
$keys = $keys_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<style>
/* 卡密管理页面专用样式 - 与脚本管理保持一致 */
.keys-container {
    max-width: 100%;
    margin: 0;
    padding: 20px;
    box-sizing: border-box;
}

.card {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 15px !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

.card h2 {
    color: white !important;
    margin-bottom: 20px !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

.form-row {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 15px !important;
    margin-bottom: 20px !important;
}

.form-group {
    display: flex !important;
    flex-direction: column !important;
}

.form-group label {
    color: white !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    margin-bottom: 8px !important;
}

.form-group input, .form-group select {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 8px !important;
    padding: 10px 12px !important;
    color: white !important;
    font-size: 13px !important;
}

.form-inline {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: 15px !important;
    margin-bottom: 20px !important;
}

.btn {
    padding: 10px 20px !important;
    font-size: 14px !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
}

table {
    width: 100% !important;
    border-collapse: collapse !important;
    font-size: 13px !important;
}

table th {
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    padding: 15px 12px !important;
    text-align: left !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
}

table td {
    padding: 15px 12px !important;
    color: rgba(255, 255, 255, 0.9) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    vertical-align: middle !important;
}

/* 毛玻璃按钮效果 */
.glass-btn {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    transition: all 0.3s ease !important;
}

.glass-btn:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}

/* 功能类型徽章样式 */
.function-badge {
    padding: 4px 8px !important;
    border-radius: 12px !important;
    font-size: 11px !important;
    font-weight: bold !important;
    color: white !important;
    text-align: center !important;
    white-space: nowrap !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

.function-badge.full-features {
    background: linear-gradient(135deg, #ff6b9d, #c44569) !important;
}

.function-badge.product-listing {
    background: linear-gradient(135deg, #4ecdc4, #44a08d) !important;
}

.function-badge.customer-service {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
}

.function-badge.no-features {
    background: #666 !important;
}

/* 卡密类型徽章样式 */
.card-type-badge {
    display: inline-block;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    font-size: 12px !important;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 15px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    min-width: 50px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.card-type-badge::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.5s;
}

.card-type-badge:hover::before {
    animation: shine 0.5s ease-in-out;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* 不同卡密类型的颜色方案 */
.card-type-badge.card-type-yearly {
    background: linear-gradient(135deg, #4CAF50, #45a049); /* 年卡绿色 */
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

.card-type-badge.card-type-half_yearly {
    background: linear-gradient(135deg, #FFC107, #FF8F00); /* 半年卡黄色 */
    box-shadow: 0 2px 6px rgba(255, 193, 7, 0.3);
}

.card-type-badge.card-type-monthly {
    background: linear-gradient(135deg, #F44336, #D32F2F); /* 月卡红色 */
    box-shadow: 0 2px 6px rgba(244, 67, 54, 0.3);
}

.card-type-badge.card-type-daily {
    background: linear-gradient(135deg, #9E9E9E, #757575); /* 天卡灰色 */
    box-shadow: 0 2px 6px rgba(158, 158, 158, 0.3);
}

.card-type-badge.card-type-hourly {
    background: linear-gradient(135deg, #607D8B, #455A64); /* 小时卡深灰色 */
    box-shadow: 0 2px 6px rgba(96, 125, 139, 0.3);
}

/* 店铺类型徽章样式 */
.store-type-badge {
    display: inline-block;
    font-size: 12px !important;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 15px;
    text-align: center;
    min-width: 40px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.store-type-badge.single-store {
    background: linear-gradient(135deg, #F44336, #D32F2F); /* 单店红色 */
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    box-shadow: 0 2px 6px rgba(244, 67, 54, 0.3);
}

.store-type-badge.multi-store {
    background: linear-gradient(135deg, #4CAF50, #45a049); /* 多店绿色 */
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}
</style>

<div class="keys-container">
    <div class="card">
        <h2><i class="fas fa-plus-circle"></i> 生成卡密</h2>
        <?php echo $message; ?>
        
        <form method="POST">
            <div class="form-row">
                <div class="form-group">
                    <label><i class="fas fa-store"></i> 店铺名称 (可选)</label>
                    <input type="text" name="store_name" placeholder="请输入店铺名称">
                </div>
                <div class="form-group">
                    <label><i class="fas fa-id-card"></i> 微信小店ID (可选)</label>
                    <input type="text" name="wechat_store_id" placeholder="请输入微信小店ID">
                </div>

            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label><i class="fas fa-clock"></i> 卡密类型</label>
                    <select name="type" required>
                        <option value="hourly">小时卡 (1小时)</option>
                        <option value="daily">天卡 (1天)</option>
                        <option value="monthly" selected>月卡 (1个月)</option>
                        <option value="half_yearly">半年卡 (6个月)</option>
                        <option value="yearly">年卡 (1年)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label><i class="fas fa-sort-numeric-up"></i> 生成数量</label>
                    <input type="number" name="quantity" value="1" min="1" max="100" required>
                </div>
            </div>
            
            <!-- 卡密功能设置 -->
            <div class="form-row">
                <div class="form-group">
                    <label><i class="fas fa-cogs"></i> 卡密功能</label>
                    <div style="display: flex; flex-direction: column; gap: 15px; padding: 20px; background: rgba(255, 255, 255, 0.08); border-radius: 12px; border: 2px solid rgba(255, 255, 255, 0.15);">
                        <label style="display: flex; align-items: center; gap: 15px; cursor: pointer; color: white; font-size: 16px; margin: 0; padding: 15px 20px; background: rgba(255, 255, 255, 0.1); border-radius: 10px; border: 2px solid rgba(255, 255, 255, 0.2); transition: all 0.3s ease;">
                            <input type="checkbox" name="has_customer_service" value="1" checked style="margin: 0; transform: scale(1.3);">
                            <span style="font-weight: 600;">小梅花AI客服-微信小店</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 15px; cursor: pointer; color: white; font-size: 16px; margin: 0; padding: 15px 20px; background: rgba(255, 255, 255, 0.1); border-radius: 10px; border: 2px solid rgba(255, 255, 255, 0.2); transition: all 0.3s ease;">
                            <input type="checkbox" name="has_product_listing" value="1" style="margin: 0; transform: scale(1.3);">
                            <span style="font-weight: 600;">小梅花AI客服-抖店</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <button type="submit" name="generate_key" class="btn btn-primary glass-btn">
                <i class="fas fa-magic"></i> 生成卡密
            </button>
        </form>
        
        <?php if ($generated_key): ?>
        <div style="margin-top: 25px; background: rgba(0,0,0,0.3); padding: 20px; border-radius: 12px;">
            <h4 style="color: white; margin-bottom: 15px;"><i class="fas fa-key"></i> 生成的卡密：</h4>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; font-family: monospace; color: white; font-size: 14px; line-height: 1.6;">
                <?php echo $generated_key; ?>
            </div>
            <button onclick="copyToClipboard()" class="btn btn-secondary" style="margin-top: 10px;">
                <i class="fas fa-copy"></i> 复制卡密
            </button>
        </div>
        <?php endif; ?>
    </div>

    <?php if ($edit_key): ?>
    <div class="card">
        <h2><i class="fas fa-edit"></i> 编辑卡密</h2>
        
        <form method="POST">
            <input type="hidden" name="key_id" value="<?php echo $edit_key['id']; ?>">
            
            <div class="form-inline">
                <div class="form-group">
                    <label><i class="fas fa-key"></i> 卡密值</label>
                    <input type="text" name="key_value" value="<?php echo htmlspecialchars($edit_key['key_value']); ?>" required style="font-family: monospace;">
                </div>
            </div>

            <!-- 日期设置区域 -->
            <div class="form-group">
                <label><i class="fas fa-calendar"></i> 到期时间设置</label>
                <div style="background: rgba(255, 255, 255, 0.05); padding: 25px; border-radius: 12px; margin-bottom: 20px;">
                    <!-- 快捷升级选项 -->
                    <div style="margin-bottom: 20px;">
                        <label style="color: white; font-size: 16px; margin-bottom: 15px; display: block; font-weight: 600;">
                            <i class="fas fa-magic"></i> 快捷升级选项
                        </label>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 15px;">
                            <label style="display: flex; align-items: center; gap: 10px; cursor: pointer; color: white; font-size: 14px; margin: 0; background: rgba(255, 255, 255, 0.1); padding: 12px 16px; border-radius: 8px; transition: all 0.3s ease; border: 2px solid transparent;" class="upgrade-option">
                                <input type="radio" name="upgrade_type" value="yearly" style="margin: 0; transform: scale(1.2);">
                                <span>📅 升级年卡 (+1年)</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 10px; cursor: pointer; color: white; font-size: 14px; margin: 0; background: rgba(255, 255, 255, 0.1); padding: 12px 16px; border-radius: 8px; transition: all 0.3s ease; border: 2px solid transparent;" class="upgrade-option">
                                <input type="radio" name="upgrade_type" value="half_yearly" style="margin: 0; transform: scale(1.2);">
                                <span>📆 升级半年卡 (+6个月)</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 10px; cursor: pointer; color: white; font-size: 14px; margin: 0; background: rgba(255, 255, 255, 0.1); padding: 12px 16px; border-radius: 8px; transition: all 0.3s ease; border: 2px solid transparent;" class="upgrade-option">
                                <input type="radio" name="upgrade_type" value="monthly" style="margin: 0; transform: scale(1.2);">
                                <span>📋 升级月卡 (+1个月)</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 10px; cursor: pointer; color: white; font-size: 14px; margin: 0; background: rgba(255, 255, 255, 0.1); padding: 12px 16px; border-radius: 8px; transition: all 0.3s ease; border: 2px solid transparent;" class="upgrade-option">
                                <input type="radio" name="upgrade_type" value="daily" style="margin: 0; transform: scale(1.2);">
                                <span>📄 升级天卡 (+1天)</span>
                            </label>
                        </div>
                    </div>

                    <!-- 分隔线 -->
                    <div style="border-top: 1px dashed rgba(255, 255, 255, 0.2); margin: 20px 0; position: relative;">
                        <span style="position: absolute; top: -12px; left: 50%; transform: translateX(-50%); background: rgba(30, 30, 30, 0.9); padding: 0 15px; color: rgba(255, 255, 255, 0.6); font-size: 14px; font-weight: 500;">或</span>
                    </div>

                    <!-- 手动设置日期 -->
                    <div>
                        <label style="color: white; font-size: 16px; margin-bottom: 15px; display: block; font-weight: 600;">
                            <i class="fas fa-edit"></i> 手动设置到期时间
                        </label>
                        <div style="display: grid; grid-template-columns: 1fr auto; gap: 15px; align-items: center;">
                            <input type="text"
                                   name="manual_expiry_date"
                                   placeholder="格式: 2025-8-20-00:00"
                                   style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 8px; padding: 12px 16px; color: white; font-size: 14px; font-family: monospace;"
                                   pattern="^\d{4}-\d{1,2}-\d{1,2}-\d{2}:\d{2}$"
                                   title="请输入格式如: 2025-8-20-00:00">
                            <input type="datetime-local"
                                   name="expiry_date"
                                   value="<?php echo date('Y-m-d\TH:i', strtotime($edit_key['expiry_date'])); ?>"
                                   style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 8px; padding: 12px 16px; color: white; font-size: 14px; min-width: 200px;">
                        </div>
                        <small style="color: rgba(255, 255, 255, 0.6); font-size: 12px; margin-top: 8px; display: block;">
                            💡 可以直接输入如 "2025-8-20-00:00" 格式，或使用右侧的日期选择器
                        </small>
                    </div>

                    <!-- 当前到期时间显示 -->
                    <div style="margin-top: 20px; padding: 15px; background: rgba(255, 255, 255, 0.05); border-radius: 8px; border-left: 4px solid #28a745;">
                        <div style="color: rgba(255, 255, 255, 0.8); font-size: 14px;">
                            <i class="fas fa-clock"></i> 当前到期时间:
                            <strong style="color: white; font-size: 16px;"><?php echo date('Y年m月d日 H:i', strtotime($edit_key['expiry_date'])); ?></strong>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-inline">
                <div class="form-group">
                    <label><i class="fas fa-store"></i> 店铺名称</label>
                    <input type="text" name="store_name" value="<?php echo htmlspecialchars($edit_key['store_name'] ?? ''); ?>" placeholder="可选">
                </div>
                <div class="form-group">
                    <label><i class="fas fa-id-card"></i> 微信小店ID</label>
                    <input type="text" name="wechat_store_id" value="<?php echo htmlspecialchars($edit_key['wechat_store_id'] ?? ''); ?>" placeholder="可选">
                </div>
            </div>

            <div class="form-inline">
                <div class="form-group">
                    <label><i class="fas fa-mobile-alt"></i> 绑定手机号码</label>
                    <input type="tel" name="phone_number" value="<?php echo htmlspecialchars($edit_key['phone_number'] ?? ''); ?>" placeholder="手机号码（可选）" maxlength="11" pattern="^1[3-9]\d{9}$">
                    <small style="color: rgba(255,255,255,0.7); margin-top: 5px; display: block;">
                        <i class="fas fa-info-circle"></i> 绑定后用户可使用手机号码登录
                    </small>
                </div>

            </div>
            
            <!-- 卡密功能设置 -->
            <div class="form-inline">
                <div class="form-group">
                    <label><i class="fas fa-cogs"></i> 卡密功能</label>
                    <div style="display: flex; flex-direction: column; gap: 15px; padding: 20px; background: rgba(255, 255, 255, 0.08); border-radius: 12px; border: 2px solid rgba(255, 255, 255, 0.15);">
                        <label style="display: flex; align-items: center; gap: 15px; cursor: pointer; color: white; font-size: 16px; margin: 0; padding: 15px 20px; background: rgba(255, 255, 255, 0.1); border-radius: 10px; border: 2px solid rgba(255, 255, 255, 0.2); transition: all 0.3s ease;">
                            <input type="checkbox" name="has_customer_service" value="1" <?php echo ($edit_key['has_customer_service'] ?? 1) ? 'checked' : ''; ?> style="margin: 0; transform: scale(1.3);">
                            <span style="font-weight: 600;">小梅花AI客服-微信小店</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 15px; cursor: pointer; color: white; font-size: 16px; margin: 0; padding: 15px 20px; background: rgba(255, 255, 255, 0.1); border-radius: 10px; border: 2px solid rgba(255, 255, 255, 0.2); transition: all 0.3s ease;">
                            <input type="checkbox" name="has_product_listing" value="1" <?php echo ($edit_key['has_product_listing'] ?? 0) ? 'checked' : ''; ?> style="margin: 0; transform: scale(1.3);">
                            <span style="font-weight: 600;">小梅花AI客服-抖店</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <div style="display: flex; gap: 15px;">
                <button type="submit" name="edit_key" class="btn btn-primary">
                    <i class="fas fa-save"></i> 保存修改
                </button>
                <a href="index.php?page=keys" class="btn btn-secondary">
                    <i class="fas fa-times"></i> 取消
                </a>
            </div>
        </form>
    </div>
    <?php endif; ?>

    <div class="card">
        <h2><i class="fas fa-list"></i> 卡密管理</h2>
        
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; flex-wrap: wrap; gap: 15px;">
            <form method="GET" style="display: flex; gap: 10px; align-items: center;">
                <input type="hidden" name="page" value="keys">
                <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="搜索卡密/店铺名称..." style="width: 250px;">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> 搜索
                </button>
                <?php if ($search): ?>
                    <a href="index.php?page=keys" class="btn btn-secondary">
                        <i class="fas fa-times"></i> 清除
                    </a>
                <?php endif; ?>
            </form>
            
            <div style="display: flex; gap: 10px;">
                <form method="POST" style="display: inline;">
                    <button type="submit" name="delete_expired" class="btn btn-danger glass-btn" onclick="return confirm('确定要删除所有过期卡密吗？')">
                        <i class="fas fa-trash"></i> 清理过期
                    </button>
                </form>
            </div>
        </div>
        
        <?php if (empty($keys)): ?>
            <p style="color: rgba(255,255,255,0.7); text-align: center; padding: 40px;">
                <i class="fas fa-info-circle"></i> 
                <?php echo $search ? '没有找到匹配的卡密' : '暂无卡密数据'; ?>
            </p>
        <?php else: ?>
            <div class="table-container" style="overflow-x: auto;">
                <table style="min-width: 1400px;">
                    <thead>
                        <tr>
                            <th style="width: 180px;"><i class="fas fa-key"></i> 卡密</th>
                            <th style="width: 100px; text-align: center;"><i class="fas fa-tag"></i> 卡密类型</th>
                            <th style="width: 120px; text-align: center;"><i class="fas fa-star"></i> 卡密功能</th>
                            <th style="width: 110px;"><i class="fas fa-mobile-alt"></i> 绑定手机</th>
                            <th style="width: 120px;"><i class="fas fa-store"></i> 店铺信息</th>
                            <th style="width: 70px; text-align: center;"><i class="fas fa-toggle-on"></i> 状态</th>
                            <th style="width: 120px;"><i class="fas fa-calendar"></i> 到期时间</th>
                            <th style="width: 100px;"><i class="fas fa-heartbeat"></i> 最后活动</th>
                            <th style="width: 140px; text-align: center;"><i class="fas fa-cogs"></i> 操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($keys as $key): ?>
                        <tr>
                            <td style="font-family: monospace; font-size: 11px; word-break: break-all;">
                                <?php echo htmlspecialchars($key['key_value']); ?>
                            </td>
                            <td style="text-align: center;">
                                <div style="display: flex; flex-direction: column; align-items: center; gap: 8px;">
                                    <span class="card-type-badge card-type-<?php echo $key['type']; ?>">
                                        <?php
                                        $type_names = [
                                            'yearly' => '年卡',
                                            'half_yearly' => '半年卡',
                                            'monthly' => '月卡',
                                            'daily' => '天卡',
                                            'hourly' => '小时卡'
                                        ];
                                        echo $type_names[$key['type']] ?? $key['type'];
                                        ?>
                                    </span>
                                    <span class="store-type-badge single-store">
                                        单店
                                    </span>
                                </div>
                            </td>
                            <td style="text-align: center;">
                                <?php
                                // 修复：正确的默认值应该都是0
                                $has_customer = $key['has_customer_service'] ?? 0;
                                $has_product = $key['has_product_listing'] ?? 0;

                                if ($has_customer && $has_product) {
                                    echo '<span class="function-badge full-features">微信小店+抖店</span>';
                                } elseif ($has_product && !$has_customer) {
                                    echo '<span class="function-badge product-listing">小梅花AI客服-抖店</span>';
                                } elseif ($has_customer && !$has_product) {
                                    echo '<span class="function-badge customer-service">小梅花AI客服-微信小店</span>';
                                } else {
                                    echo '<span class="function-badge no-features">无功能</span>';
                                }
                                ?>
                            </td>
                            <td>
                                <?php if (!empty($key['phone_number'])): ?>
                                    <div style="font-family: monospace; font-weight: bold; color: #28a745;">
                                        <i class="fas fa-mobile-alt"></i> <?php echo htmlspecialchars($key['phone_number']); ?>
                                    </div>
                                <?php else: ?>
                                    <span style="opacity: 0.5; color: #ffc107;">
                                        <i class="fas fa-exclamation-triangle"></i> 未绑定
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($key['store_name'] || $key['wechat_store_id']): ?>
                                    <div style="font-weight: bold; margin-bottom: 2px;"><?php echo htmlspecialchars($key['store_name'] ?: '未设置'); ?></div>
                                    <small style="opacity: 0.7;">ID: <?php echo htmlspecialchars($key['wechat_store_id'] ?: '未设置'); ?></small>
                                <?php else: ?>
                                    <span style="opacity: 0.5;">未设置</span>
                                <?php endif; ?>
                            </td>
                            <td style="text-align: center;">
                                <?php
                                $now = new DateTime();
                                $expiry = new DateTime($key['expiry_date']);
                                $is_expired = $now > $expiry;
                                $status_class = $is_expired ? 'status-expired' : ($key['status'] == 'active' ? 'status-active' : 'status-banned');
                                $status_text = $is_expired ? '已过期' : ($key['status'] == 'active' ? '有效' : '已禁用');
                                ?>
                                <span class="status-badge <?php echo $status_class; ?>">
                                    <?php echo $status_text; ?>
                                </span>
                            </td>
                            <td>
                                <div style="font-weight: bold; margin-bottom: 2px;"><?php echo date('Y-m-d H:i', strtotime($key['expiry_date'])); ?></div>
                                <?php if ($is_expired): ?>
                                    <small style="color: #ff6b6b;">已过期</small>
                                <?php else: ?>
                                    <?php
                                    $diff = $expiry->diff($now);
                                    if ($diff->days > 0) {
                                        echo "<small style='color: white;'>还有{$diff->days}天</small>";
                                    } elseif ($diff->h > 0) {
                                        echo "<small style='color: #ffa726;'>还有{$diff->h}小时</small>";
                                    } else {
                                        echo "<small style='color: #ff6b6b;'>即将过期</small>";
                                    }
                                    ?>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($key['last_heartbeat']): ?>
                                    <div style="margin-bottom: 2px;"><?php echo format_time_ago($key['last_heartbeat']); ?></div>
                                    <?php if ($key['last_used_ip']): ?>
                                        <small style="opacity: 0.7;"><?php echo htmlspecialchars($key['last_used_ip']); ?></small>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span style="opacity: 0.5;">从未使用</span>
                                <?php endif; ?>
                            </td>
                            <td style="text-align: center;">
                                <div style="display: flex; gap: 4px; justify-content: center; flex-wrap: wrap;">
                                    <a href="index.php?page=keys&edit_key=<?php echo $key['id']; ?>" 
                                       class="btn btn-secondary glass-btn" 
                                       style="padding: 4px 8px; font-size: 11px;"
                                       title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    
                                    <?php if (!$is_expired): ?>
                                        <a href="index.php?page=keys&toggle_status=<?php echo $key['id']; ?>&status=<?php echo $key['status']; ?>" 
                                           class="btn <?php echo $key['status'] == 'active' ? 'btn-warning' : 'btn-success'; ?> glass-btn" 
                                           style="padding: 4px 8px; font-size: 11px;"
                                           title="<?php echo $key['status'] == 'active' ? '禁用' : '启用'; ?>"
                                           onclick="return confirm('确定要<?php echo $key['status'] == 'active' ? '禁用' : '启用'; ?>这个卡密吗？')">
                                            <i class="fas <?php echo $key['status'] == 'active' ? 'fa-ban' : 'fa-check'; ?>"></i>
                                        </a>
                                    <?php endif; ?>
                                    
                                    <a href="index.php?page=keys&delete_key=<?php echo $key['id']; ?>" 
                                       class="btn btn-danger glass-btn" 
                                       style="padding: 4px 8px; font-size: 11px;"
                                       title="删除"
                                       onclick="return confirm('确定要删除这个卡密吗？')">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <?php if ($total_pages > 1): ?>
            <div style="margin-top: 20px; text-align: center;">
                <div style="display: inline-flex; gap: 5px; align-items: center;">
                    <?php if ($page_num > 1): ?>
                        <a href="index.php?page=keys&p=<?php echo $page_num - 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>" class="btn btn-secondary">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    <?php endif; ?>
                    
                    <span style="color: rgba(255,255,255,0.8); margin: 0 15px;">
                        第 <?php echo $page_num; ?> 页，共 <?php echo $total_pages; ?> 页 (<?php echo $total_keys; ?> 条记录)
                    </span>
                    
                    <?php if ($page_num < $total_pages): ?>
                        <a href="index.php?page=keys&p=<?php echo $page_num + 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>" class="btn btn-secondary">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<script>
function copyToClipboard() {
    const text = `<?php echo str_replace('<br>', '\n', $generated_key); ?>`;
    navigator.clipboard.writeText(text).then(() => {
        alert('卡密已复制到剪贴板！');
    }).catch(() => {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('卡密已复制到剪贴板！');
    });
}

// 日期设置功能初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeDateSettings();
});

function initializeDateSettings() {
    const upgradeOptions = document.querySelectorAll('input[name="upgrade_type"]');
    const manualDateInput = document.querySelector('input[name="manual_expiry_date"]');
    const datetimeInput = document.querySelector('input[name="expiry_date"]');

    if (!upgradeOptions.length || !manualDateInput || !datetimeInput) return;

    // 升级选项样式处理
    upgradeOptions.forEach(option => {
        const label = option.closest('.upgrade-option');

        option.addEventListener('change', function() {
            // 重置所有选项样式
            document.querySelectorAll('.upgrade-option').forEach(opt => {
                opt.style.background = 'rgba(255, 255, 255, 0.1)';
                opt.style.borderColor = 'transparent';
            });

            // 高亮选中的选项
            if (this.checked) {
                label.style.background = 'rgba(40, 167, 69, 0.3)';
                label.style.border = '1px solid rgba(40, 167, 69, 0.5)';

                // 清空手动输入
                manualDateInput.value = '';

                // 计算新的到期时间
                calculateUpgradeDate(this.value);
            }
        });

        // 悬停效果
        label.addEventListener('mouseenter', function() {
            if (!option.checked) {
                this.style.background = 'rgba(255, 255, 255, 0.2)';
            }
        });

        label.addEventListener('mouseleave', function() {
            if (!option.checked) {
                this.style.background = 'rgba(255, 255, 255, 0.1)';
            }
        });
    });

    // 手动输入日期处理
    manualDateInput.addEventListener('input', function() {
        const value = this.value.trim();
        if (value) {
            // 清除升级选项
            upgradeOptions.forEach(option => {
                option.checked = false;
                option.closest('.upgrade-option').style.background = 'rgba(255, 255, 255, 0.1)';
                option.closest('.upgrade-option').style.borderColor = 'transparent';
            });

            // 尝试解析手动输入的日期
            parseManualDate(value);
        }
    });

    // 日期选择器变化时清除其他选项
    datetimeInput.addEventListener('change', function() {
        if (this.value) {
            // 清除升级选项
            upgradeOptions.forEach(option => {
                option.checked = false;
                option.closest('.upgrade-option').style.background = 'rgba(255, 255, 255, 0.1)';
                option.closest('.upgrade-option').style.borderColor = 'transparent';
            });

            // 清空手动输入
            manualDateInput.value = '';
        }
    });
}

// 计算升级后的日期
function calculateUpgradeDate(upgradeType) {
    const currentExpiryInput = document.querySelector('input[name="expiry_date"]');
    if (!currentExpiryInput) return;

    const currentDate = new Date(currentExpiryInput.value);
    if (isNaN(currentDate.getTime())) return;

    let newDate = new Date(currentDate);

    switch (upgradeType) {
        case 'yearly':
            newDate.setFullYear(newDate.getFullYear() + 1);
            break;
        case 'half_yearly':
            newDate.setMonth(newDate.getMonth() + 6);
            break;
        case 'monthly':
            newDate.setMonth(newDate.getMonth() + 1);
            break;
        case 'daily':
            newDate.setDate(newDate.getDate() + 1);
            break;
    }

    // 更新日期选择器的值
    const formattedDate = newDate.getFullYear() + '-' +
                         String(newDate.getMonth() + 1).padStart(2, '0') + '-' +
                         String(newDate.getDate()).padStart(2, '0') + 'T' +
                         String(newDate.getHours()).padStart(2, '0') + ':' +
                         String(newDate.getMinutes()).padStart(2, '0');

    currentExpiryInput.value = formattedDate;
}

// 解析手动输入的日期格式
function parseManualDate(dateStr) {
    const datetimeInput = document.querySelector('input[name="expiry_date"]');
    if (!datetimeInput) return;

    // 支持格式: 2025-8-20-00:00
    const match = dateStr.match(/^(\d{4})-(\d{1,2})-(\d{1,2})-(\d{2}):(\d{2})$/);
    if (match) {
        const [, year, month, day, hour, minute] = match;

        // 验证日期有效性
        const date = new Date(year, month - 1, day, hour, minute);
        if (date.getFullYear() == year &&
            date.getMonth() == month - 1 &&
            date.getDate() == day) {

            // 格式化为datetime-local格式
            const formattedDate = year + '-' +
                                 String(month).padStart(2, '0') + '-' +
                                 String(day).padStart(2, '0') + 'T' +
                                 String(hour).padStart(2, '0') + ':' +
                                 String(minute).padStart(2, '0');

            datetimeInput.value = formattedDate;
        }
    }
}
</script>