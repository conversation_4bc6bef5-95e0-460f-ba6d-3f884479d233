<?php
/**
 * 手机号码绑定功能测试脚本
 * 用于测试APP手机号码绑定和网站后台显示功能
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入数据库配置
require_once 'config.php';

// 设置响应头
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手机号码绑定功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: white;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .test-title {
            color: #4CAF50;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .test-result {
            color: white;
            line-height: 1.6;
        }
        .success {
            color: #4CAF50;
        }
        .error {
            color: #f44336;
        }
        .warning {
            color: #ff9800;
        }
        .info {
            color: #2196F3;
        }
        pre {
            background: rgba(0, 0, 0, 0.3);
            color: white;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            color: white;
            display: block;
            margin-bottom: 5px;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            box-sizing: border-box;
        }
        input[type="text"]::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 手机号码绑定功能测试</h1>

        <?php
        try {
            // 测试1: 数据库连接
            echo '<div class="test-section">';
            echo '<div class="test-title">📊 测试1: 数据库连接</div>';
            echo '<div class="test-result">';
            
            $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            echo '<span class="success">✅ 数据库连接成功</span><br>';
            echo '</div></div>';

            // 测试2: 检查数据库表结构
            echo '<div class="test-section">';
            echo '<div class="test-title">🗃️ 测试2: 数据库表结构检查</div>';
            echo '<div class="test-result">';
            
            // 检查license_keys表是否存在phone_number字段
            $stmt = $pdo->query("DESCRIBE license_keys");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $has_phone_column = false;
            
            foreach ($columns as $column) {
                if ($column['Field'] === 'phone_number') {
                    $has_phone_column = true;
                    break;
                }
            }
            
            if ($has_phone_column) {
                echo '<span class="success">✅ license_keys表包含phone_number字段</span><br>';
            } else {
                echo '<span class="error">❌ license_keys表缺少phone_number字段</span><br>';
                echo '<span class="warning">⚠️ 需要执行数据库升级脚本</span><br>';
            }
            
            // 检查phone_login_logs表
            try {
                $stmt = $pdo->query("DESCRIBE phone_login_logs");
                echo '<span class="success">✅ phone_login_logs表存在</span><br>';
            } catch (PDOException $e) {
                echo '<span class="warning">⚠️ phone_login_logs表不存在（可选）</span><br>';
            }
            
            echo '</div></div>';

            // 测试3: 查看现有卡密的手机号码绑定情况
            echo '<div class="test-section">';
            echo '<div class="test-title">📱 测试3: 现有卡密手机号码绑定情况</div>';
            echo '<div class="test-result">';
            
            if ($has_phone_column) {
                $stmt = $pdo->query("SELECT key_value, phone_number, store_name FROM license_keys ORDER BY id DESC LIMIT 10");
                $keys = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (empty($keys)) {
                    echo '<span class="warning">⚠️ 数据库中没有卡密数据</span><br>';
                } else {
                    echo '<span class="info">📋 最近10个卡密的绑定情况：</span><br>';
                    echo '<pre>';
                    foreach ($keys as $key) {
                        $phone_status = $key['phone_number'] ? $key['phone_number'] : '未绑定';
                        $store_name = $key['store_name'] ? $key['store_name'] : '未设置';
                        echo "卡密: " . substr($key['key_value'], 0, 8) . "... | 手机号: {$phone_status} | 店铺: {$store_name}\n";
                    }
                    echo '</pre>';
                }
            } else {
                echo '<span class="error">❌ 无法检查，phone_number字段不存在</span><br>';
            }
            
            echo '</div></div>';

            // 测试4: API接口测试
            echo '<div class="test-section">';
            echo '<div class="test-title">🔌 测试4: API接口测试</div>';
            echo '<div class="test-result">';
            
            if (isset($_POST['test_api'])) {
                $test_key = $_POST['test_key'];
                $test_phone = $_POST['test_phone'];
                
                if ($test_key && $test_phone) {
                    echo '<span class="info">🧪 测试API调用...</span><br>';
                    
                    // 模拟API调用
                    $api_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/api/verify.php';
                    
                    $post_data = [
                        'key' => $test_key,
                        'phone_number' => $test_phone,
                        'bind_phone' => 1,
                        'device_id' => 'test-device-' . time(),
                        'version' => '1.0.14'
                    ];
                    
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $api_url);
                    curl_setopt($ch, CURLOPT_POST, true);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_data));
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                    
                    $response = curl_exec($ch);
                    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);
                    
                    if ($response === false) {
                        echo '<span class="error">❌ API调用失败</span><br>';
                    } else {
                        echo '<span class="success">✅ API响应 (HTTP ' . $http_code . '):</span><br>';
                        echo '<pre>' . htmlspecialchars($response) . '</pre>';
                        
                        $json_response = json_decode($response, true);
                        if ($json_response && isset($json_response['phone_number'])) {
                            echo '<span class="success">✅ API返回包含phone_number字段</span><br>';
                        } else {
                            echo '<span class="warning">⚠️ API返回不包含phone_number字段</span><br>';
                        }
                    }
                } else {
                    echo '<span class="warning">⚠️ 请填写测试卡密和手机号码</span><br>';
                }
            }
            
            echo '<form method="POST">';
            echo '<div class="form-group">';
            echo '<label>测试卡密:</label>';
            echo '<input type="text" name="test_key" placeholder="输入要测试的卡密" value="' . ($_POST['test_key'] ?? '') . '">';
            echo '</div>';
            echo '<div class="form-group">';
            echo '<label>测试手机号码:</label>';
            echo '<input type="text" name="test_phone" placeholder="输入要绑定的手机号码" value="' . ($_POST['test_phone'] ?? '') . '">';
            echo '</div>';
            echo '<button type="submit" name="test_api">测试API绑定</button>';
            echo '</form>';
            
            echo '</div></div>';

        } catch (Exception $e) {
            echo '<div class="test-section">';
            echo '<div class="test-title">❌ 错误</div>';
            echo '<div class="test-result">';
            echo '<span class="error">测试过程中发生错误: ' . htmlspecialchars($e->getMessage()) . '</span>';
            echo '</div></div>';
        }
        ?>

        <div class="test-section">
            <div class="test-title">📋 测试总结</div>
            <div class="test-result">
                <p><strong>修复内容：</strong></p>
                <ul style="color: white;">
                    <li>✅ 修复了API返回数据中缺少phone_number字段的问题</li>
                    <li>✅ 修复了网站后台卡密管理页面手机号码显示位置错误的问题</li>
                    <li>✅ 优化了手机号码显示样式，已绑定显示绿色，未绑定显示黄色</li>
                </ul>
                
                <p><strong>下一步：</strong></p>
                <ul style="color: white;">
                    <li>🔄 重新打包APP软件</li>
                    <li>🧪 测试完整的绑定流程</li>
                    <li>📱 验证APP设置页面显示</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
