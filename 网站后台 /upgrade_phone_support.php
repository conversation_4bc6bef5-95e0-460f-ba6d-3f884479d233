<?php
/**
 * 数据库升级脚本：添加手机号码绑定支持
 * 版本：v1.0
 * 日期：2025-08-21
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置内容类型
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>数据库升级 - 手机号码绑定支持</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>数据库升级 - 手机号码绑定支持</h1>
";

function logMessage($message, $type = 'info') {
    $class = $type;
    echo "<p class='$class'>$message</p>\n";
    flush();
}

try {
    // 尝试包含数据库配置文件
    $config_files = [
        'includes/db.php',
        'config/database.php',
        'api/config.php'
    ];
    
    $pdo = null;
    foreach ($config_files as $config_file) {
        if (file_exists($config_file)) {
            logMessage("找到配置文件: $config_file", 'info');
            require_once $config_file;
            if (isset($pdo) && $pdo instanceof PDO) {
                logMessage("数据库连接成功", 'success');
                break;
            }
        }
    }
    
    if (!$pdo) {
        throw new Exception("无法建立数据库连接，请检查配置文件");
    }
    
    // 开始升级过程
    logMessage("开始数据库升级过程...", 'info');
    
    // 1. 检查当前表结构
    logMessage("步骤1: 检查当前表结构", 'info');
    
    $stmt = $pdo->query("SHOW COLUMNS FROM license_keys LIKE 'phone_number'");
    $phone_field_exists = $stmt->rowCount() > 0;
    
    if ($phone_field_exists) {
        logMessage("✅ phone_number字段已存在", 'success');
    } else {
        logMessage("⚠️ phone_number字段不存在，需要添加", 'warning');
    }
    
    // 2. 执行升级SQL
    logMessage("步骤2: 执行数据库升级", 'info');
    
    $sql_file = '10_database_phone_number_support.sql';
    if (!file_exists($sql_file)) {
        throw new Exception("升级SQL文件不存在: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    if ($sql_content === false) {
        throw new Exception("无法读取升级SQL文件");
    }
    
    // 分割SQL语句并执行
    $statements = explode(';', $sql_content);
    $executed = 0;
    $errors = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            $executed++;
        } catch (Exception $e) {
            // 忽略一些预期的错误（如字段已存在）
            if (strpos($e->getMessage(), 'Duplicate column name') !== false ||
                strpos($e->getMessage(), 'already exists') !== false) {
                logMessage("跳过已存在的结构: " . substr($statement, 0, 50) . "...", 'info');
            } else {
                logMessage("执行SQL失败: " . $e->getMessage(), 'error');
                logMessage("SQL: " . substr($statement, 0, 100) . "...", 'error');
                $errors++;
            }
        }
    }
    
    logMessage("SQL执行完成: 成功 $executed 条，错误 $errors 条", $errors > 0 ? 'warning' : 'success');
    
    // 3. 验证升级结果
    logMessage("步骤3: 验证升级结果", 'info');
    
    // 检查phone_number字段
    $stmt = $pdo->query("SHOW COLUMNS FROM license_keys LIKE 'phone_number'");
    if ($stmt->rowCount() > 0) {
        $column_info = $stmt->fetch();
        logMessage("✅ phone_number字段已成功添加", 'success');
        logMessage("字段类型: " . $column_info['Type'], 'info');
    } else {
        logMessage("❌ phone_number字段添加失败", 'error');
    }
    
    // 检查索引
    $stmt = $pdo->query("SHOW INDEX FROM license_keys WHERE Key_name = 'idx_phone_number'");
    if ($stmt->rowCount() > 0) {
        logMessage("✅ phone_number索引已成功创建", 'success');
    } else {
        logMessage("⚠️ phone_number索引可能未创建", 'warning');
    }
    
    // 检查phone_login_logs表
    $stmt = $pdo->query("SHOW TABLES LIKE 'phone_login_logs'");
    if ($stmt->rowCount() > 0) {
        logMessage("✅ phone_login_logs表已成功创建", 'success');
    } else {
        logMessage("⚠️ phone_login_logs表可能未创建", 'warning');
    }
    
    // 4. 显示最终状态
    logMessage("步骤4: 显示最终状态", 'info');
    
    $stmt = $pdo->query("DESCRIBE license_keys");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>license_keys表结构:</h3>";
    echo "<pre>";
    foreach ($columns as $column) {
        $highlight = $column['Field'] === 'phone_number' ? ' <-- 新增字段' : '';
        echo sprintf("%-20s %-20s %-10s %-10s%s\n", 
            $column['Field'], 
            $column['Type'], 
            $column['Null'], 
            $column['Default'],
            $highlight
        );
    }
    echo "</pre>";
    
    logMessage("🎉 数据库升级完成！", 'success');
    logMessage("现在可以使用手机号码绑定功能了", 'success');
    
} catch (Exception $e) {
    logMessage("升级失败: " . $e->getMessage(), 'error');
    logMessage("请检查数据库连接和权限设置", 'error');
}

echo "</body></html>";
?>
