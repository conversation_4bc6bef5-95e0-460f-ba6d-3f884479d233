<?php
// 获取flash消息函数
function getFlashMessage() {
    $messages = [];
    
    if (isset($_SESSION['flash_success'])) {
        $messages['success'] = $_SESSION['flash_success'];
        unset($_SESSION['flash_success']);
    }
    
    if (isset($_SESSION['flash_error'])) {
        $messages['error'] = $_SESSION['flash_error'];
        unset($_SESSION['flash_error']);
    }
    
    return $messages;
}

// POST重定向函数
function handlePostRedirect($page, $success_message = '', $error_message = '', $generated_keys = []) {
    // 将消息存储到session中
    if (!empty($success_message)) {
        $_SESSION['flash_success'] = $success_message;
    }
    if (!empty($error_message)) {
        $_SESSION['flash_error'] = $error_message;
    }
    if (!empty($generated_keys)) {
        $_SESSION['generated_keys'] = $generated_keys;
    }
    
    // 重定向到指定页面
    $redirect_url = "index.php?page=" . urlencode($page);
    header("Location: " . $redirect_url);
    exit;
}

// 获取flash消息
$flash_messages = getFlashMessage();
$success_message = $flash_messages['success'] ?? '';
$error_message = $flash_messages['error'] ?? '';

// 获取生成的卡密
$generated_key = '';
if (isset($_SESSION['generated_keys'])) {
    $generated_key = implode('<br>', $_SESSION['generated_keys']);
    unset($_SESSION['generated_keys']);
}

$message = '';
if ($success_message) {
    $message = "<div class='message success'><i class='fas fa-check-circle'></i> {$success_message}</div>";
}
if ($error_message) {
    $message = "<div class='message error'><i class='fas fa-times-circle'></i> {$error_message}</div>";
}

// 获取所有脚本用于下拉选择
$scripts_stmt = $pdo->query("SELECT id, name, version FROM scripts ORDER BY created_at DESC");
$scripts = $scripts_stmt->fetchAll(PDO::FETCH_ASSOC);

// 生成卡密
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['generate_key'])) {
    $store_name = !empty($_POST['store_name']) ? trim($_POST['store_name']) : null;
    $wechat_store_id = !empty($_POST['wechat_store_id']) ? trim($_POST['wechat_store_id']) : null;
    $douyin_store_name = !empty($_POST['douyin_store_name']) ? trim($_POST['douyin_store_name']) : null;
    $douyin_store_id = !empty($_POST['douyin_store_id']) ? trim($_POST['douyin_store_id']) : null;
    
    // 获取额外的店铺信息
    $additional_stores = [];
    if (isset($_POST['additional_store_names']) && isset($_POST['additional_store_ids'])) {
        $additional_store_names = $_POST['additional_store_names'];
        $additional_store_ids = $_POST['additional_store_ids'];
        
        for ($i = 0; $i < count($additional_store_names); $i++) {
            if (!empty($additional_store_names[$i]) && !empty($additional_store_ids[$i])) {
                $additional_stores[] = [
                    'name' => trim($additional_store_names[$i]),
                    'id' => trim($additional_store_ids[$i])
                ];
            }
        }
    }
    
    // 检查是否为多店铺卡密
    $is_multi_store = !empty($additional_stores);

    $type = $_POST['type'];
    $quantity = intval($_POST['quantity'] ?? 1);
    
    // 功能权限设置 - 修复：检查值而不是是否存在
    $has_customer_service = (isset($_POST['has_customer_service']) && $_POST['has_customer_service'] == '1') ? 1 : 0;
    $has_product_listing = (isset($_POST['has_product_listing']) && $_POST['has_product_listing'] == '1') ? 1 : 0;

    // 验证卡密功能必须选择
    if (!$has_customer_service && !$has_product_listing) {
        $message = "<div class='message error'><i class='fas fa-exclamation-triangle'></i> 必须选择至少一个卡密功能</div>";
    } elseif ($has_customer_service && (empty($store_name) || empty($wechat_store_id))) {
        $message = "<div class='message error'><i class='fas fa-exclamation-triangle'></i> 选择小梅花AI客服-微信小店功能时，微信店铺名称和微信小店ID为必填项</div>";
    } elseif ($has_product_listing && (empty($douyin_store_name) || empty($douyin_store_id))) {
        $message = "<div class='message error'><i class='fas fa-exclamation-triangle'></i> 选择小梅花AI客服-抖店功能时，抖店店铺名称和抖店ID为必填项</div>";
    } elseif ($quantity < 1 || $quantity > 100) {
        $message = "<div class='message error'><i class='fas fa-exclamation-triangle'></i> 生成数量必须在1-100之间</div>";
    } else {
        $expiry_interval = '';
        switch ($type) {
            case 'yearly': $expiry_interval = '+1 year'; break;
            case 'half_yearly': $expiry_interval = '+6 months'; break;
            case 'monthly': $expiry_interval = '+1 month'; break;
            case 'daily': $expiry_interval = '+1 day'; break;
            case 'hourly': $expiry_interval = '+1 hour'; break;
        }

        if ($expiry_interval) {
            $generated_keys = [];
            $errors = [];

            for ($i = 0; $i < $quantity; $i++) {
                try {
                    $new_key = generate_key();
                    $expiry_date = (new DateTime())->modify($expiry_interval)->format('Y-m-d H:i:s');

                    // 验证生成的卡密
                    if (empty($new_key)) {
                        $error_msg = "生成的卡密为空";
                        $errors[] = $error_msg;
                        error_log($error_msg);
                        continue;
                    }

                    // 检查卡密是否已存在
                    $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM license_keys WHERE key_value = ?");
                    $check_stmt->execute([$new_key]);
                    if ($check_stmt->fetchColumn() > 0) {
                        // 如果卡密已存在，重新生成
                        $i--;
                        continue;
                    }

                    // 验证必要参数
                    if (empty($type)) {
                        $error_msg = "卡密类型不能为空";
                        $errors[] = $error_msg;
                        error_log($error_msg);
                        continue;
                    }

                    // 开始事务
                    $pdo->beginTransaction();

                    try {
                        // 检查数据库是否支持抖店字段
                        $has_douyin_fields = false;
                        try {
                            $check_stmt = $pdo->query("SHOW COLUMNS FROM license_keys LIKE 'douyin_store_name'");
                            $has_douyin_fields = $check_stmt->rowCount() > 0;
                        } catch (Exception $e) {
                            // 忽略检查错误，默认不支持
                        }

                        // 根据数据库支持情况选择不同的插入语句
                        if ($has_douyin_fields) {
                            // 支持抖店字段的完整插入
                            $stmt = $pdo->prepare(
                                "INSERT INTO license_keys (key_value, type, store_name, wechat_store_id, douyin_store_name, douyin_store_id, expiry_date, status, has_customer_service, has_product_listing, is_multi_store, created_at)
                                 VALUES (?, ?, ?, ?, ?, ?, ?, 'active', ?, ?, ?, NOW())"
                            );
                            $execute_params = [$new_key, $type, $store_name, $wechat_store_id, $douyin_store_name, $douyin_store_id, $expiry_date, $has_customer_service, $has_product_listing, $is_multi_store ? 1 : 0];
                        } else {
                            // 不支持抖店字段的基础插入
                            $stmt = $pdo->prepare(
                                "INSERT INTO license_keys (key_value, type, store_name, wechat_store_id, expiry_date, status, has_customer_service, has_product_listing, created_at)
                                 VALUES (?, ?, ?, ?, ?, 'active', ?, ?, NOW())"
                            );
                            $execute_params = [$new_key, $type, $store_name, $wechat_store_id, $expiry_date, $has_customer_service, $has_product_listing];
                        }

                        $execute_result = $stmt->execute($execute_params);

                        if ($execute_result) {
                            $key_id = $pdo->lastInsertId();

                            // 如果有额外的店铺，插入关联记录
                            if ($is_multi_store) {
                                foreach ($additional_stores as $store) {
                                    $store_stmt = $pdo->prepare(
                                        "INSERT INTO license_key_stores (license_key_id, store_name, wechat_store_id, created_at)
                                         VALUES (?, ?, ?, NOW())"
                                    );
                                    $store_stmt->execute([$key_id, $store['name'], $store['id']]);
                                }
                            }

                            $pdo->commit();
                            $generated_keys[] = $new_key;
                            error_log("成功生成卡密: $new_key (类型: $type, 过期时间: $expiry_date)");
                        } else {
                            $pdo->rollBack();
                            $error_info = $stmt->errorInfo();
                            $error_msg = "卡密插入失败 - SQLSTATE: " . $error_info[0] . ", 错误代码: " . $error_info[1] . ", 错误信息: " . $error_info[2];
                            $errors[] = $error_msg;
                            error_log($error_msg);
                            error_log("插入参数详情: key=$new_key, type=$type, store_name=$store_name, wechat_store_id=$wechat_store_id, expiry_date=$expiry_date");
                        }
                    } catch (Exception $e) {
                        $pdo->rollBack();
                        $error_msg = "生成卡密事务失败: " . $e->getMessage();
                        $errors[] = $error_msg;
                        error_log($error_msg);
                    }
                } catch (Exception $e) {
                    $error_msg = "生成卡密异常: " . $e->getMessage();
                    $errors[] = $error_msg;
                    error_log($error_msg);
                }
            }

            if (!empty($generated_keys)) {
                handlePostRedirect('keys', '成功生成 ' . count($generated_keys) . ' 个卡密', '', $generated_keys);
            } else {
                $error_detail = !empty($errors) ? ' (' . implode(', ', array_slice($errors, 0, 3)) . ')' : '';
                handlePostRedirect('keys', '', '生成卡密失败' . $error_detail);
            }
        }
    }
}

// 删除卡密
if (isset($_GET['delete_key'])) {
    $key_id = $_GET['delete_key'];
    $stmt = $pdo->prepare("DELETE FROM license_keys WHERE id = ?");
    if ($stmt->execute([$key_id])) {
        handlePostRedirect('keys', '卡密已删除！');
    } else {
        handlePostRedirect('keys', '', '删除失败');
    }
}

// 禁用/启用卡密
if (isset($_GET['toggle_status'])) {
    $key_id = $_GET['toggle_status'];
    $new_status = $_GET['status'] === 'active' ? 'banned' : 'active';
    $stmt = $pdo->prepare("UPDATE license_keys SET status = ? WHERE id = ?");
    if ($stmt->execute([$new_status, $key_id])) {
        $status_text = $new_status === 'active' ? '启用' : '禁用';
        handlePostRedirect('keys', "卡密已{$status_text}！");
    } else {
        handlePostRedirect('keys', '', '操作失败');
    }
}

// 编辑卡密
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['edit_key'])) {
    $key_id = $_POST['key_id'];
    $new_key_value = trim($_POST['key_value']);

    // 处理日期设置逻辑
    $new_expiry_date = '';
    $new_type = null; // 新增：用于存储升级后的卡密类型

    // 优先处理升级选项
    if (!empty($_POST['upgrade_type'])) {
        // 获取当前到期时间
        $current_stmt = $pdo->prepare("SELECT expiry_date FROM license_keys WHERE id = ?");
        $current_stmt->execute([$key_id]);
        $current_expiry = $current_stmt->fetchColumn();

        if ($current_expiry) {
            $current_date = new DateTime($current_expiry);

            switch ($_POST['upgrade_type']) {
                case 'yearly':
                    $current_date->modify('+1 year');
                    $new_type = 'yearly'; // 设置新的卡密类型
                    break;
                case 'half_yearly':
                    $current_date->modify('+6 months');
                    $new_type = 'half_yearly'; // 设置新的卡密类型
                    break;
                case 'monthly':
                    $current_date->modify('+1 month');
                    $new_type = 'monthly'; // 设置新的卡密类型
                    break;
                case 'daily':
                    $current_date->modify('+1 day');
                    $new_type = 'daily'; // 设置新的卡密类型
                    break;
            }

            $new_expiry_date = $current_date->format('Y-m-d H:i:s');
        }
    }
    // 处理手动输入的日期格式 (2025-8-20-00:00)
    elseif (!empty($_POST['manual_expiry_date'])) {
        $manual_date = trim($_POST['manual_expiry_date']);
        if (preg_match('/^(\d{4})-(\d{1,2})-(\d{1,2})-(\d{2}):(\d{2})$/', $manual_date, $matches)) {
            $year = $matches[1];
            $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
            $day = str_pad($matches[3], 2, '0', STR_PAD_LEFT);
            $hour = $matches[4];
            $minute = $matches[5];

            $new_expiry_date = "{$year}-{$month}-{$day} {$hour}:{$minute}:00";
        }
    }
    // 使用标准日期选择器的值
    elseif (!empty($_POST['expiry_date'])) {
        $new_expiry_date = $_POST['expiry_date'];
    }

    $store_name = !empty($_POST['store_name']) ? trim($_POST['store_name']) : null;
    $wechat_store_id = !empty($_POST['wechat_store_id']) ? trim($_POST['wechat_store_id']) : null;
    $douyin_store_name = !empty($_POST['edit_douyin_store_name']) ? trim($_POST['edit_douyin_store_name']) : null;
    $douyin_store_id = !empty($_POST['edit_douyin_store_id']) ? trim($_POST['edit_douyin_store_id']) : null;
    
    // 获取额外的店铺信息
    $additional_stores = [];
    if (isset($_POST['additional_store_names']) && isset($_POST['additional_store_ids'])) {
        $additional_store_names = $_POST['additional_store_names'];
        $additional_store_ids = $_POST['additional_store_ids'];
        
        for ($i = 0; $i < count($additional_store_names); $i++) {
            if (!empty($additional_store_names[$i]) && !empty($additional_store_ids[$i])) {
                $additional_stores[] = [
                    'name' => trim($additional_store_names[$i]),
                    'id' => trim($additional_store_ids[$i])
                ];
            }
        }
    }
    
    // 检查是否为多店铺卡密
    $is_multi_store = !empty($additional_stores);
    
    // 功能权限设置 - 修复：检查值而不是是否存在
    $has_customer_service = (isset($_POST['edit_has_customer_service']) && $_POST['edit_has_customer_service'] == '1') ? 1 : 0;
    $has_product_listing = (isset($_POST['edit_has_product_listing']) && $_POST['edit_has_product_listing'] == '1') ? 1 : 0;

    // 验证卡密功能必须选择
    if (!$has_customer_service && !$has_product_listing) {
        handlePostRedirect('keys', '', '必须选择至少一个卡密功能');
        return;
    }

    // 验证微信店铺信息 - 只有选择微信小店功能时才验证
    if ($has_customer_service && (empty($store_name) || empty($wechat_store_id))) {
        handlePostRedirect('keys', '', '选择小梅花AI客服-微信小店功能时，微信店铺名称和微信小店ID为必填项');
        return;
    }

    // 验证抖店信息 - 只有选择抖店功能时才验证
    if ($has_product_listing && (empty($douyin_store_name) || empty($douyin_store_id))) {
        handlePostRedirect('keys', '', '选择小梅花AI客服-抖店功能时，抖店店铺名称和抖店ID为必填项');
        return;
    }

    if (!empty($new_key_value) && !empty($new_expiry_date)) {
        // 检查新卡密是否已存在
        $check_stmt = $pdo->prepare("SELECT id FROM license_keys WHERE key_value = ? AND id != ?");
        $check_stmt->execute([$new_key_value, $key_id]);
        
        if ($check_stmt->fetchColumn()) {
            handlePostRedirect('keys', '', '卡密已存在，请使用其他卡密');
        } else {
            // 开始事务
            $pdo->beginTransaction();
            
            try {
                // 检查数据库是否支持抖店字段
                $has_douyin_fields = false;
                try {
                    $check_stmt = $pdo->query("SHOW COLUMNS FROM license_keys LIKE 'douyin_store_name'");
                    $has_douyin_fields = $check_stmt->rowCount() > 0;
                } catch (Exception $e) {
                    // 忽略检查错误，默认不支持
                }

                // 根据数据库支持情况选择不同的更新语句
                if ($has_douyin_fields) {
                    // 支持抖店字段的完整更新
                    if ($new_type) {
                        $stmt = $pdo->prepare("UPDATE license_keys SET key_value = ?, type = ?, expiry_date = ?, store_name = ?, wechat_store_id = ?, douyin_store_name = ?, douyin_store_id = ?, has_customer_service = ?, has_product_listing = ?, is_multi_store = ? WHERE id = ?");
                        $execute_params = [$new_key_value, $new_type, $new_expiry_date, $store_name, $wechat_store_id, $douyin_store_name, $douyin_store_id, $has_customer_service, $has_product_listing, $is_multi_store ? 1 : 0, $key_id];
                    } else {
                        $stmt = $pdo->prepare("UPDATE license_keys SET key_value = ?, expiry_date = ?, store_name = ?, wechat_store_id = ?, douyin_store_name = ?, douyin_store_id = ?, has_customer_service = ?, has_product_listing = ?, is_multi_store = ? WHERE id = ?");
                        $execute_params = [$new_key_value, $new_expiry_date, $store_name, $wechat_store_id, $douyin_store_name, $douyin_store_id, $has_customer_service, $has_product_listing, $is_multi_store ? 1 : 0, $key_id];
                    }
                } else {
                    // 不支持抖店字段的基础更新
                    if ($new_type) {
                        $stmt = $pdo->prepare("UPDATE license_keys SET key_value = ?, type = ?, expiry_date = ?, store_name = ?, wechat_store_id = ?, has_customer_service = ?, has_product_listing = ? WHERE id = ?");
                        $execute_params = [$new_key_value, $new_type, $new_expiry_date, $store_name, $wechat_store_id, $has_customer_service, $has_product_listing, $key_id];
                    } else {
                        $stmt = $pdo->prepare("UPDATE license_keys SET key_value = ?, expiry_date = ?, store_name = ?, wechat_store_id = ?, has_customer_service = ?, has_product_listing = ? WHERE id = ?");
                        $execute_params = [$new_key_value, $new_expiry_date, $store_name, $wechat_store_id, $has_customer_service, $has_product_listing, $key_id];
                    }
                }

                if ($stmt->execute($execute_params)) {
                    
                    // 删除所有现有的额外店铺记录
                    $delete_stmt = $pdo->prepare("DELETE FROM license_key_stores WHERE license_key_id = ?");
                    $delete_stmt->execute([$key_id]);
                    
                    // 如果有额外的店铺，插入新的关联记录
                    if ($is_multi_store) {
                        foreach ($additional_stores as $store) {
                            $store_stmt = $pdo->prepare(
                                "INSERT INTO license_key_stores (license_key_id, store_name, wechat_store_id, created_at) 
                                 VALUES (?, ?, ?, NOW())"
                            );
                            $store_stmt->execute([$key_id, $store['name'], $store['id']]);
                        }
                    }
                    
                    $pdo->commit();
                    handlePostRedirect('keys', '卡密信息已更新！');
                } else {
                    $pdo->rollBack();
                    handlePostRedirect('keys', '', '更新失败');
                }
            } catch (Exception $e) {
                $pdo->rollBack();
                error_log("更新卡密失败: " . $e->getMessage());
                handlePostRedirect('keys', '', '更新失败: ' . $e->getMessage());
            }
        }
    } else {
        handlePostRedirect('keys', '', '卡密和到期时间不能为空');
    }
}

// 获取要编辑的卡密
$edit_key = null;
$additional_stores = [];
if (isset($_GET['edit_key'])) {
    $stmt = $pdo->prepare("SELECT * FROM license_keys WHERE id = ?");
    $stmt->execute([$_GET['edit_key']]);
    $edit_key = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // 如果是多店铺卡密，获取额外的店铺信息
    if ($edit_key && $edit_key['is_multi_store']) {
        $stores_stmt = $pdo->prepare("SELECT * FROM license_key_stores WHERE license_key_id = ? ORDER BY id ASC");
        $stores_stmt->execute([$_GET['edit_key']]);
        $additional_stores = $stores_stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}

// 批量删除过期卡密
if (isset($_POST['delete_expired'])) {
    $stmt = $pdo->prepare("DELETE FROM license_keys WHERE status = 'expired' OR expiry_date <= NOW()");
    $deleted_count = $stmt->execute() ? $stmt->rowCount() : 0;
    handlePostRedirect('keys', "已删除 {$deleted_count} 个过期卡密");
}

// 搜索和分页
$search = $_GET['search'] ?? '';
$page_num = max(1, intval($_GET['p'] ?? 1));
$per_page = 20;
$offset = ($page_num - 1) * $per_page;

$where_clause = "";
$params = [];
if (!empty($search)) {
    $where_clause = "WHERE lk.key_value LIKE ? OR lk.store_name LIKE ? OR lk.wechat_store_id LIKE ?";
    $search_param = "%{$search}%";
    $params = [$search_param, $search_param, $search_param];
}

// 获取总数
$count_sql = "SELECT COUNT(*) FROM license_keys lk " . $where_clause;
$count_stmt = $pdo->prepare($count_sql);
$count_stmt->execute($params);
$total_keys = $count_stmt->fetchColumn();
$total_pages = ceil($total_keys / $per_page);

// 获取卡密列表（移除绑定脚本功能）
$keys_sql = "
    SELECT lk.*
    FROM license_keys lk
    {$where_clause}
    ORDER BY lk.id DESC
    LIMIT {$per_page} OFFSET {$offset}
";
$keys_stmt = $pdo->prepare($keys_sql);
$keys_stmt->execute($params);
$keys = $keys_stmt->fetchAll(PDO::FETCH_ASSOC);

// 获取多店铺卡密的额外店铺信息
$keys_with_stores = [];
foreach ($keys as $key) {
    $key_id = $key['id'];
    $key['additional_stores'] = [];
    
    // 如果是多店铺卡密，获取额外的店铺信息
    if ($key['is_multi_store']) {
        $stores_stmt = $pdo->prepare("SELECT store_name, wechat_store_id FROM license_key_stores WHERE license_key_id = ? ORDER BY id ASC");
        $stores_stmt->execute([$key_id]);
        $key['additional_stores'] = $stores_stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    $keys_with_stores[] = $key;
}
$keys = $keys_with_stores;
?>

<style>
/* 卡密管理页面专用样式 - 与脚本管理保持一致 */
.keys-container {
    max-width: 100%;
    margin: 0;
    padding: 20px;
    box-sizing: border-box;
}

.card {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 15px !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

.card h2 {
    color: white !important;
    margin-bottom: 20px !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

.form-row {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 15px !important;
    margin-bottom: 20px !important;
}

.form-group {
    display: flex !important;
    flex-direction: column !important;
}

.form-group label {
    color: white !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    margin-bottom: 8px !important;
}

.form-group input, .form-group select {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 8px !important;
    padding: 10px 12px !important;
    color: white !important;
    font-size: 13px !important;
}

.form-inline {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: 15px !important;
    margin-bottom: 20px !important;
}

.btn {
    padding: 10px 20px !important;
    font-size: 14px !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
}

table {
    width: 100% !important;
    border-collapse: collapse !important;
    font-size: 13px !important;
}

table th {
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    padding: 15px 12px !important;
    text-align: left !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
}

table td {
    padding: 15px 12px !important;
    color: rgba(255, 255, 255, 0.9) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    vertical-align: middle !important;
}

/* 毛玻璃按钮效果 */
.glass-btn {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    transition: all 0.3s ease !important;
}

.glass-btn:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}

/* 功能类型徽章样式 */
.function-badge {
    padding: 4px 8px !important;
    border-radius: 12px !important;
    font-size: 11px !important;
    font-weight: bold !important;
    color: white !important;
    text-align: center !important;
    white-space: nowrap !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

.function-badge.full-features {
    background: linear-gradient(135deg, #ff6b9d, #c44569) !important;
}

.function-badge.product-listing {
    background: linear-gradient(135deg, #4ecdc4, #44a08d) !important;
}

.function-badge.customer-service {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
}

.function-badge.no-features {
    background: #666 !important;
}

/* 多店铺卡密样式 */
.multi-store-badge {
    display: inline-block;
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
    font-size: 11px;
    font-weight: bold;
    padding: 5px 10px;
    border-radius: 10px;
    margin-left: 5px;
    vertical-align: middle;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.multi-store-badge::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
    transform: rotate(35deg);
    pointer-events: none;
}

.store-info {
    margin-bottom: 2px;
}

.more-stores-btn {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    cursor: pointer;
    margin-top: 5px;
    transition: all 0.3s ease;
}

.more-stores-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 店铺弹窗样式 */
.stores-modal {
    display: none; /* 初始状态为隐藏 */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99999; /* 确保在最顶层 */
    /* 移除背景遮罩和模糊效果 */
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes modalFadeOut {
    from {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    to {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
}

.stores-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    background: #ffffff; /* 改为白色背景 */
    border-radius: 12px; /* 减小圆角 */
    padding: 24px;
    width: 550px;
    max-width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15); /* 减轻阴影 */
    border: 1px solid rgba(0, 0, 0, 0.1); /* 改为浅色边框 */
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 100000;
}

.stores-modal-content.active {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

.stores-modal-content.closing {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
}

.stores-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1); /* 改为深色边框 */
}

.stores-modal-title {
    color: #333333; /* 改为深色文字 */
    font-size: 20px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 10px;
}

.stores-modal-title i {
    color: #ff6b9d;
}

.stores-modal-close {
    color: #000000; /* 改为纯黑色 */
    background: rgba(0, 0, 0, 0.08); /* 增加背景透明度 */
    border: 2px solid rgba(0, 0, 0, 0.2); /* 增加边框透明度 */
    font-size: 28px; /* 增大字体 */
    font-weight: bold; /* 加粗 */
    cursor: pointer;
    padding: 0;
    line-height: 1;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15); /* 增强阴影 */
}

.stores-modal-close:hover {
    background: rgba(220, 53, 69, 0.15); /* 悬停时改为红色背景 */
    color: #dc3545; /* 悬停时改为红色文字 */
    border-color: rgba(220, 53, 69, 0.4); /* 悬停时改为红色边框 */
    transform: rotate(90deg) scale(1.1); /* 增加缩放效果 */
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3); /* 悬停时增强阴影 */
}

.stores-modal-close:active {
    transform: rotate(90deg) scale(0.95); /* 点击时稍微缩小 */
}

.store-item {
    background: #f8f9fa; /* 改为浅灰色背景 */
    border-radius: 8px; /* 减小圆角 */
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.store-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1); /* 减轻阴影 */
}

.store-item.main-store {
    border-left-color: #28a745;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.02)); /* 减轻背景色 */
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.1); /* 减轻阴影 */
}

.store-item.additional-store {
    border-left-color: #ff6b9d;
    background: linear-gradient(135deg, rgba(255, 107, 157, 0.1), rgba(255, 107, 157, 0.02)); /* 减轻背景色 */
    box-shadow: 0 2px 8px rgba(255, 107, 157, 0.1); /* 减轻阴影 */
}

/* 移除伪元素，不需要额外的背景层 */

.store-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: #6c757d; /* 改为灰色背景 */
    color: white;
    font-size: 11px;
    font-weight: bold;
    padding: 4px 10px;
    border-bottom-left-radius: 6px; /* 减小圆角 */
    border-top-right-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* 减轻阴影 */
}

.main-store .store-badge {
    background: #28a745; /* 改为纯色背景 */
}

.additional-store .store-badge {
    background: #ff6b9d; /* 改为纯色背景 */
}

.store-name {
    color: #333333; /* 改为深色文字 */
    font-weight: bold;
    margin-bottom: 8px;
    font-size: 15px;
    margin-top: 15px;
}

.store-id {
    color: #666666; /* 改为深灰色文字 */
    font-size: 13px;
    padding-top: 5px;
    border-top: 1px dashed rgba(0, 0, 0, 0.1); /* 改为深色虚线 */
}

/* 卡密类型徽章样式 */
.card-type-badge {
    display: inline-block;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    font-size: 12px !important;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 15px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    min-width: 50px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.card-type-badge::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.5s;
}

.card-type-badge:hover::before {
    animation: shine 0.5s ease-in-out;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* 不同卡密类型的颜色方案 */
.card-type-badge.card-type-yearly {
    background: linear-gradient(135deg, #4CAF50, #45a049); /* 年卡绿色 */
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

.card-type-badge.card-type-half_yearly {
    background: linear-gradient(135deg, #FFC107, #FF8F00); /* 半年卡黄色 */
    box-shadow: 0 2px 6px rgba(255, 193, 7, 0.3);
}

.card-type-badge.card-type-monthly {
    background: linear-gradient(135deg, #F44336, #D32F2F); /* 月卡红色 */
    box-shadow: 0 2px 6px rgba(244, 67, 54, 0.3);
}

.card-type-badge.card-type-daily {
    background: linear-gradient(135deg, #9E9E9E, #757575); /* 天卡灰色 */
    box-shadow: 0 2px 6px rgba(158, 158, 158, 0.3);
}

.card-type-badge.card-type-hourly {
    background: linear-gradient(135deg, #607D8B, #455A64); /* 小时卡深灰色 */
    box-shadow: 0 2px 6px rgba(96, 125, 139, 0.3);
}

/* 店铺类型徽章样式 */
.store-type-badge {
    display: inline-block;
    font-size: 12px !important;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 15px;
    text-align: center;
    min-width: 40px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.store-type-badge.single-store {
    background: linear-gradient(135deg, #F44336, #D32F2F); /* 单店红色 */
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    box-shadow: 0 2px 6px rgba(244, 67, 54, 0.3);
}

.store-type-badge.multi-store {
    background: linear-gradient(135deg, #4CAF50, #45a049); /* 多店绿色 */
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

/* 权限卡片样式 */
.permission-cards-container {
    display: flex !important;
    gap: 20px !important;
    flex-wrap: wrap !important;
    margin-top: 10px !important;
}

.permission-card {
    width: 180px !important;
    height: 180px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 15px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: relative !important;
    overflow: hidden !important;
}

.permission-card.selected {
    background: rgba(138, 43, 226, 0.3) !important;
    border-color: rgba(138, 43, 226, 0.5) !important;
    box-shadow: 0 0 20px rgba(138, 43, 226, 0.3) !important;
}

.permission-card-content {
    text-align: center !important;
    color: white !important;
    z-index: 2 !important;
    position: relative !important;
}

.permission-title {
    font-size: 18px !important;
    font-weight: 600 !important;
    margin-bottom: 8px !important;
    line-height: 1.2 !important;
}

.permission-subtitle {
    font-size: 16px !important;
    font-weight: 500 !important;
    opacity: 0.9 !important;
    line-height: 1.2 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .permission-cards-container {
        flex-direction: column !important;
        align-items: center !important;
    }

    .permission-card {
        width: 180px !important;
        height: 180px !important;
    }
}
</style>

<div class="keys-container">
    <div class="card">
        <h2><i class="fas fa-plus-circle"></i> 生成卡密</h2>
        <?php echo $message; ?>
        
        <form method="POST">
            <!-- 微信店铺信息区域 - 默认显示，根据功能选择动态设置必填 -->
            <div id="wechat-store-section" class="form-row">
                <div class="form-group">
                    <label><i class="fas fa-store"></i> 微信店铺名称 <span id="wechat-store-name-required" style="color: #ff6b6b; display: none;">*</span></label>
                    <input type="text" name="store_name" placeholder="请输入微信店铺名称（可选）">
                </div>
                <div class="form-group">
                    <label><i class="fas fa-id-card"></i> 微信小店ID <span id="wechat-store-id-required" style="color: #ff6b6b; display: none;">*</span></label>
                    <input type="text" name="wechat_store_id" placeholder="请输入微信小店ID（可选）">
                </div>
            </div>

            <!-- 抖店信息区域 - 默认显示，根据功能选择动态设置必填 -->
            <div id="douyin-store-section" class="form-row">
                <div class="form-group">
                    <label><i class="fas fa-store"></i> 抖店店铺名称 <span id="douyin-store-name-required" style="color: #ff6b6b; display: none;">*</span></label>
                    <input type="text" name="douyin_store_name" placeholder="请输入抖店店铺名称（可选）">
                </div>
                <div class="form-group">
                    <label><i class="fas fa-id-card"></i> 抖店ID <span id="douyin-store-id-required" style="color: #ff6b6b; display: none;">*</span></label>
                    <input type="text" name="douyin_store_id" placeholder="请输入抖店ID（可选）">
                </div>
            </div>
            
            <div id="additional-stores-container">
                <!-- 额外的店铺信息将在这里动态添加 -->
            </div>
            
            <div style="margin-bottom: 20px;">
                <button type="button" id="add-wechat-store-btn" class="btn btn-secondary glass-btn" style="margin-right: 10px;">
                    <i class="fas fa-plus-circle"></i> 新增微信店铺
                </button>
                <button type="button" id="add-douyin-store-btn" class="btn btn-secondary glass-btn">
                    <i class="fas fa-plus-circle"></i> 新增抖店店铺
                </button>
                <small style="margin-left: 10px; color: rgba(255,255,255,0.7); display: block; margin-top: 8px;">
                    添加多个店铺将使卡密变成多店卡密
                </small>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label><i class="fas fa-clock"></i> 卡密类型</label>
                    <select name="type" required>
                        <option value="hourly">小时卡 (1小时)</option>
                        <option value="daily">天卡 (1天)</option>
                        <option value="monthly" selected>月卡 (1个月)</option>
                        <option value="half_yearly">半年卡 (6个月)</option>
                        <option value="yearly">年卡 (1年)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label><i class="fas fa-sort-numeric-up"></i> 生成数量</label>
                    <input type="number" name="quantity" value="1" min="1" max="100" required>
                </div>
            </div>
            
            <!-- 卡密功能设置 -->
            <div class="form-row">
                <div class="form-group">
                    <label><i class="fas fa-cogs"></i> 卡密功能</label>
                    <div class="permission-cards-container">
                        <div class="permission-card" data-permission="customer_service" onclick="togglePermissionCard(this, 'has_customer_service')">
                            <div class="permission-card-content">
                                <div class="permission-title">小梅花AI客服</div>
                                <div class="permission-subtitle">微信小店</div>
                            </div>
                        </div>
                        <div class="permission-card" data-permission="product_listing" onclick="togglePermissionCard(this, 'has_product_listing')">
                            <div class="permission-card-content">
                                <div class="permission-title">小梅花AI客服</div>
                                <div class="permission-subtitle">抖店</div>
                            </div>
                        </div>
                    </div>

                    <!-- 隐藏的input用于表单提交 -->
                    <input type="hidden" name="has_customer_service" id="has_customer_service" value="0">
                    <input type="hidden" name="has_product_listing" id="has_product_listing" value="0">
                </div>
            </div>
            
            <button type="submit" name="generate_key" class="btn btn-primary glass-btn">
                <i class="fas fa-magic"></i> 生成卡密
            </button>
        </form>
        
        <?php if ($generated_key): ?>
        <div style="margin-top: 25px; background: rgba(0,0,0,0.3); padding: 20px; border-radius: 12px;">
            <h4 style="color: white; margin-bottom: 15px;"><i class="fas fa-key"></i> 生成的卡密：</h4>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; font-family: monospace; color: white; font-size: 14px; line-height: 1.6;">
                <?php echo $generated_key; ?>
            </div>
            <button onclick="copyToClipboard()" class="btn btn-secondary" style="margin-top: 10px;">
                <i class="fas fa-copy"></i> 复制卡密
            </button>
        </div>
        <?php endif; ?>
    </div>

    <?php if ($edit_key): ?>
    <div class="card">
        <h2><i class="fas fa-edit"></i> 编辑卡密</h2>
        
        <form method="POST">
            <input type="hidden" name="key_id" value="<?php echo $edit_key['id']; ?>">
            
            <div class="form-inline">
                <div class="form-group">
                    <label><i class="fas fa-key"></i> 卡密值</label>
                    <input type="text" name="key_value" value="<?php echo htmlspecialchars($edit_key['key_value']); ?>" required style="font-family: monospace;">
                </div>
            </div>

            <!-- 日期设置区域 -->
            <div class="form-group">
                <label><i class="fas fa-calendar"></i> 到期时间设置</label>
                <div style="background: rgba(255, 255, 255, 0.05); padding: 25px; border-radius: 12px; margin-bottom: 20px;">
                    <!-- 快捷升级选项 -->
                    <div style="margin-bottom: 20px;">
                        <label style="color: white; font-size: 16px; margin-bottom: 15px; display: block; font-weight: 600;">
                            <i class="fas fa-magic"></i> 快捷升级选项
                        </label>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 15px;">
                            <label style="display: flex; align-items: center; gap: 10px; cursor: pointer; color: white; font-size: 14px; margin: 0; background: rgba(255, 255, 255, 0.1); padding: 12px 16px; border-radius: 8px; transition: all 0.3s ease; border: 2px solid transparent;" class="upgrade-option">
                                <input type="radio" name="upgrade_type" value="yearly" style="margin: 0; transform: scale(1.2);">
                                <span>📅 升级年卡 (+1年)</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 10px; cursor: pointer; color: white; font-size: 14px; margin: 0; background: rgba(255, 255, 255, 0.1); padding: 12px 16px; border-radius: 8px; transition: all 0.3s ease; border: 2px solid transparent;" class="upgrade-option">
                                <input type="radio" name="upgrade_type" value="half_yearly" style="margin: 0; transform: scale(1.2);">
                                <span>📆 升级半年卡 (+6个月)</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 10px; cursor: pointer; color: white; font-size: 14px; margin: 0; background: rgba(255, 255, 255, 0.1); padding: 12px 16px; border-radius: 8px; transition: all 0.3s ease; border: 2px solid transparent;" class="upgrade-option">
                                <input type="radio" name="upgrade_type" value="monthly" style="margin: 0; transform: scale(1.2);">
                                <span>📋 升级月卡 (+1个月)</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 10px; cursor: pointer; color: white; font-size: 14px; margin: 0; background: rgba(255, 255, 255, 0.1); padding: 12px 16px; border-radius: 8px; transition: all 0.3s ease; border: 2px solid transparent;" class="upgrade-option">
                                <input type="radio" name="upgrade_type" value="daily" style="margin: 0; transform: scale(1.2);">
                                <span>📄 升级天卡 (+1天)</span>
                            </label>
                        </div>
                    </div>

                    <!-- 分隔线 -->
                    <div style="border-top: 1px dashed rgba(255, 255, 255, 0.2); margin: 20px 0; position: relative;">
                        <span style="position: absolute; top: -12px; left: 50%; transform: translateX(-50%); background: rgba(30, 30, 30, 0.9); padding: 0 15px; color: rgba(255, 255, 255, 0.6); font-size: 14px; font-weight: 500;">或</span>
                    </div>

                    <!-- 手动设置日期 -->
                    <div>
                        <label style="color: white; font-size: 16px; margin-bottom: 15px; display: block; font-weight: 600;">
                            <i class="fas fa-edit"></i> 手动设置到期时间
                        </label>
                        <div style="display: grid; grid-template-columns: 1fr auto; gap: 15px; align-items: center;">
                            <input type="text"
                                   name="manual_expiry_date"
                                   placeholder="格式: 2025-8-20-00:00"
                                   style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 8px; padding: 12px 16px; color: white; font-size: 14px; font-family: monospace;"
                                   pattern="^\d{4}-\d{1,2}-\d{1,2}-\d{2}:\d{2}$"
                                   title="请输入格式如: 2025-8-20-00:00">
                            <input type="datetime-local"
                                   name="expiry_date"
                                   value="<?php echo date('Y-m-d\TH:i', strtotime($edit_key['expiry_date'])); ?>"
                                   style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 8px; padding: 12px 16px; color: white; font-size: 14px; min-width: 200px;">
                        </div>
                        <small style="color: rgba(255, 255, 255, 0.6); font-size: 12px; margin-top: 8px; display: block;">
                            💡 可以直接输入如 "2025-8-20-00:00" 格式，或使用右侧的日期选择器
                        </small>
                    </div>

                    <!-- 当前到期时间显示 -->
                    <div style="margin-top: 20px; padding: 15px; background: rgba(255, 255, 255, 0.05); border-radius: 8px; border-left: 4px solid #28a745;">
                        <div style="color: rgba(255, 255, 255, 0.8); font-size: 14px;">
                            <i class="fas fa-clock"></i> 当前到期时间:
                            <strong style="color: white; font-size: 16px;"><?php echo date('Y年m月d日 H:i', strtotime($edit_key['expiry_date'])); ?></strong>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 微信店铺信息区域 - 默认显示，根据功能选择动态设置必填 -->
            <div id="edit-wechat-store-section" class="form-inline">
                <div class="form-group">
                    <label><i class="fas fa-store"></i> 微信店铺名称 <span id="edit-wechat-store-name-required" style="color: #ff6b6b; display: <?php echo ($edit_key['has_customer_service'] ?? 0) ? 'inline' : 'none'; ?>;">*</span></label>
                    <input type="text" name="store_name" value="<?php echo htmlspecialchars($edit_key['store_name'] ?? ''); ?>"
                           placeholder="请输入微信店铺名称<?php echo ($edit_key['has_customer_service'] ?? 0) ? '' : '（可选）'; ?>"
                           <?php echo ($edit_key['has_customer_service'] ?? 0) ? 'required' : ''; ?>>
                </div>
                <div class="form-group">
                    <label><i class="fas fa-id-card"></i> 微信小店ID <span id="edit-wechat-store-id-required" style="color: #ff6b6b; display: <?php echo ($edit_key['has_customer_service'] ?? 0) ? 'inline' : 'none'; ?>;">*</span></label>
                    <input type="text" name="wechat_store_id" value="<?php echo htmlspecialchars($edit_key['wechat_store_id'] ?? ''); ?>"
                           placeholder="请输入微信小店ID<?php echo ($edit_key['has_customer_service'] ?? 0) ? '' : '（可选）'; ?>"
                           <?php echo ($edit_key['has_customer_service'] ?? 0) ? 'required' : ''; ?>>
                </div>
            </div>

            <!-- 抖店信息区域 - 默认显示，根据功能选择动态设置必填 -->
            <div id="edit-douyin-store-section" class="form-inline">
                <div class="form-group">
                    <label><i class="fas fa-store"></i> 抖店店铺名称 <span id="edit-douyin-store-name-required" style="color: #ff6b6b; display: <?php echo ($edit_key['has_product_listing'] ?? 0) ? 'inline' : 'none'; ?>;">*</span></label>
                    <input type="text" name="edit_douyin_store_name" value="<?php echo htmlspecialchars($edit_key['douyin_store_name'] ?? ''); ?>"
                           placeholder="请输入抖店店铺名称<?php echo ($edit_key['has_product_listing'] ?? 0) ? '' : '（可选）'; ?>"
                           <?php echo ($edit_key['has_product_listing'] ?? 0) ? 'required' : ''; ?>>
                </div>
                <div class="form-group">
                    <label><i class="fas fa-id-card"></i> 抖店ID <span id="edit-douyin-store-id-required" style="color: #ff6b6b; display: <?php echo ($edit_key['has_product_listing'] ?? 0) ? 'inline' : 'none'; ?>;">*</span></label>
                    <input type="text" name="edit_douyin_store_id" value="<?php echo htmlspecialchars($edit_key['douyin_store_id'] ?? ''); ?>"
                           placeholder="请输入抖店ID<?php echo ($edit_key['has_product_listing'] ?? 0) ? '' : '（可选）'; ?>"
                           <?php echo ($edit_key['has_product_listing'] ?? 0) ? 'required' : ''; ?>>
                </div>
            </div>
            
            <!-- 额外的店铺信息 -->
            <div id="edit-additional-stores-container">
                <?php foreach ($additional_stores as $index => $store): ?>
                <div class="form-inline additional-store">
                    <div class="form-group">
                        <label><i class="fas fa-store"></i> 额外店铺名称 #<?php echo $index + 1; ?></label>
                        <input type="text" name="additional_store_names[]" value="<?php echo htmlspecialchars($store['store_name']); ?>" required>
                    </div>
                    <div class="form-group">
                        <label><i class="fas fa-id-card"></i> 额外微信小店ID #<?php echo $index + 1; ?></label>
                        <input type="text" name="additional_store_ids[]" value="<?php echo htmlspecialchars($store['wechat_store_id']); ?>" required>
                    </div>
                    <button type="button" class="btn btn-danger remove-store-btn" style="height: 40px; margin-top: 28px;">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <?php endforeach; ?>
            </div>
            
            <div style="margin-bottom: 20px;">
                <button type="button" id="edit-add-wechat-store-btn" class="btn btn-secondary glass-btn" style="margin-right: 10px;">
                    <i class="fas fa-plus-circle"></i> 新增微信店铺
                </button>
                <button type="button" id="edit-add-douyin-store-btn" class="btn btn-secondary glass-btn">
                    <i class="fas fa-plus-circle"></i> 新增抖店店铺
                </button>
                <small style="margin-left: 10px; color: rgba(255,255,255,0.7); display: block; margin-top: 8px;">
                    添加多个店铺将使卡密变成多店卡密
                </small>
            </div>
            
            <!-- 卡密功能设置 -->
            <div class="form-inline">
                <div class="form-group">
                    <label><i class="fas fa-cogs"></i> 卡密功能</label>
                    <div class="permission-cards-container">
                        <div class="permission-card <?php echo ($edit_key['has_customer_service'] ?? 0) ? 'selected' : ''; ?>"
                             data-permission="customer_service" onclick="togglePermissionCard(this, 'edit_has_customer_service')">
                            <div class="permission-card-content">
                                <div class="permission-title">小梅花AI客服</div>
                                <div class="permission-subtitle">微信小店</div>
                            </div>
                        </div>
                        <div class="permission-card <?php echo ($edit_key['has_product_listing'] ?? 0) ? 'selected' : ''; ?>"
                             data-permission="product_listing" onclick="togglePermissionCard(this, 'edit_has_product_listing')">
                            <div class="permission-card-content">
                                <div class="permission-title">小梅花AI客服</div>
                                <div class="permission-subtitle">抖店</div>
                            </div>
                        </div>
                    </div>

                    <!-- 隐藏的input用于表单提交 -->
                    <input type="hidden" name="edit_has_customer_service" id="edit_has_customer_service" value="<?php echo ($edit_key['has_customer_service'] ?? 0) ? '1' : '0'; ?>">
                    <input type="hidden" name="edit_has_product_listing" id="edit_has_product_listing" value="<?php echo ($edit_key['has_product_listing'] ?? 0) ? '1' : '0'; ?>">
                </div>
            </div>
            
            <div style="display: flex; gap: 15px;">
                <button type="submit" name="edit_key" class="btn btn-primary">
                    <i class="fas fa-save"></i> 保存修改
                </button>
                <a href="index.php?page=keys" class="btn btn-secondary">
                    <i class="fas fa-times"></i> 取消
                </a>
            </div>
        </form>
    </div>
    <?php endif; ?>

    <div class="card">
        <h2><i class="fas fa-list"></i> 卡密管理</h2>
        
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; flex-wrap: wrap; gap: 15px;">
            <form method="GET" style="display: flex; gap: 10px; align-items: center;">
                <input type="hidden" name="page" value="keys">
                <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="搜索卡密/店铺名称..." style="width: 250px;">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> 搜索
                </button>
                <?php if ($search): ?>
                    <a href="index.php?page=keys" class="btn btn-secondary">
                        <i class="fas fa-times"></i> 清除
                    </a>
                <?php endif; ?>
            </form>
            
            <div style="display: flex; gap: 10px;">
                <form method="POST" style="display: inline;">
                    <button type="submit" name="delete_expired" class="btn btn-danger glass-btn" onclick="return confirm('确定要删除所有过期卡密吗？')">
                        <i class="fas fa-trash"></i> 清理过期
                    </button>
                </form>
            </div>
        </div>
        
        <?php if (empty($keys)): ?>
            <p style="color: rgba(255,255,255,0.7); text-align: center; padding: 40px;">
                <i class="fas fa-info-circle"></i> 
                <?php echo $search ? '没有找到匹配的卡密' : '暂无卡密数据'; ?>
            </p>
        <?php else: ?>
            <div class="table-container" style="overflow-x: auto;">
                <table style="min-width: 1400px;">
                    <thead>
                        <tr>
                            <th style="width: 180px;"><i class="fas fa-key"></i> 卡密</th>
                            <th style="width: 100px; text-align: center;"><i class="fas fa-tag"></i> 卡密类型</th>
                            <th style="width: 120px; text-align: center;"><i class="fas fa-star"></i> 卡密功能</th>
                            <th style="width: 110px;"><i class="fas fa-mobile-alt"></i> 绑定手机</th>
                            <th style="width: 120px;"><i class="fas fa-store"></i> 店铺信息</th>
                            <th style="width: 70px; text-align: center;"><i class="fas fa-toggle-on"></i> 状态</th>
                            <th style="width: 120px;"><i class="fas fa-calendar"></i> 到期时间</th>
                            <th style="width: 100px;"><i class="fas fa-heartbeat"></i> 最后活动</th>
                            <th style="width: 140px; text-align: center;"><i class="fas fa-cogs"></i> 操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($keys as $key): ?>
                        <tr>
                            <td style="font-family: monospace; font-size: 11px; word-break: break-all;">
                                <?php echo htmlspecialchars($key['key_value']); ?>
                            </td>
                            <td style="text-align: center;">
                                <div style="display: flex; flex-direction: column; align-items: center; gap: 8px;">
                                    <span class="card-type-badge card-type-<?php echo $key['type']; ?>">
                                        <?php
                                        $type_names = [
                                            'yearly' => '年卡',
                                            'half_yearly' => '半年卡',
                                            'monthly' => '月卡',
                                            'daily' => '天卡',
                                            'hourly' => '小时卡'
                                        ];
                                        echo $type_names[$key['type']] ?? $key['type'];
                                        ?>
                                    </span>
                                    <span class="store-type-badge <?php echo $key['is_multi_store'] ? 'multi-store' : 'single-store'; ?>">
                                        <?php echo $key['is_multi_store'] ? '多店' : '单店'; ?>
                                    </span>
                                </div>
                            </td>
                            <td style="text-align: center;">
                                <?php
                                // 修复：正确的默认值应该都是0
                                $has_customer = $key['has_customer_service'] ?? 0;
                                $has_product = $key['has_product_listing'] ?? 0;
                                
                                if ($has_customer && $has_product) {
                                    echo '<span class="function-badge full-features">微信小店+抖店</span>';
                                } elseif ($has_product && !$has_customer) {
                                    echo '<span class="function-badge product-listing">小梅花AI客服-抖店</span>';
                                } elseif ($has_customer && !$has_product) {
                                    echo '<span class="function-badge customer-service">小梅花AI客服-微信小店</span>';
                                } else {
                                    echo '<span class="function-badge no-features">无功能</span>';
                                }
                                ?>
                            </td>
                            <td>
                                <?php if (!empty($key['phone_number'])): ?>
                                    <div style="color: #28a745; font-weight: bold;">
                                        <i class="fas fa-mobile-alt"></i> <?php echo htmlspecialchars($key['phone_number']); ?>
                                    </div>
                                <?php else: ?>
                                    <span style="opacity: 0.5; color: #ffc107;">
                                        <i class="fas fa-mobile-alt"></i> 未绑定
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($key['store_name'] || $key['wechat_store_id'] || !empty($key['additional_stores'])): ?>
                                    <div class="store-info">
                                        <?php if ($key['store_name']): ?>
                                            <div style="font-weight: bold; margin-bottom: 2px;">
                                                <?php echo htmlspecialchars($key['store_name']); ?>
                                            </div>
                                        <?php endif; ?>

                                        <?php if ($key['wechat_store_id']): ?>
                                            <small>ID: <?php echo htmlspecialchars($key['wechat_store_id']); ?></small>
                                        <?php endif; ?>

                                        <?php if ($key['is_multi_store'] && !empty($key['additional_stores'])): ?>
                                            <div>
                                                <span class="more-stores-btn" onclick="showStoresModal(<?php echo $key['id']; ?>)">
                                                    <i class="fas fa-store"></i> 查看全部店铺 (<?php echo count($key['additional_stores']) + 1; ?>)
                                                </span>
                                            </div>

                                            <!-- 使用数据属性存储店铺信息，而不是直接在这里创建弹窗 -->
                                            <span data-store-name="<?php echo htmlspecialchars($key['store_name']); ?>"
                                                  data-store-id="<?php echo htmlspecialchars($key['wechat_store_id']); ?>"
                                                  data-key-id="<?php echo $key['id']; ?>"
                                                  data-stores-count="<?php echo count($key['additional_stores']) + 1; ?>"
                                                  data-additional-stores='<?php echo htmlspecialchars(json_encode($key['additional_stores'])); ?>'
                                                  class="stores-data-container"></span>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <span style="opacity: 0.5;">未绑定</span>
                                <?php endif; ?>
                            </td>
                            <td style="text-align: center;">
                                <?php
                                $now = new DateTime();
                                $expiry = new DateTime($key['expiry_date']);
                                $is_expired = $now > $expiry;
                                $status_class = $is_expired ? 'status-expired' : ($key['status'] == 'active' ? 'status-active' : 'status-banned');
                                $status_text = $is_expired ? '已过期' : ($key['status'] == 'active' ? '有效' : '已禁用');
                                ?>
                                <span class="status-badge <?php echo $status_class; ?>">
                                    <?php echo $status_text; ?>
                                </span>
                            </td>
                            <td>
                                <div style="font-weight: bold; margin-bottom: 2px;"><?php echo date('Y-m-d H:i', strtotime($key['expiry_date'])); ?></div>
                                <?php if ($is_expired): ?>
                                    <small style="color: #ff6b6b;">已过期</small>
                                <?php else: ?>
                                    <?php
                                    $diff = $expiry->diff($now);
                                    if ($diff->days > 0) {
                                        echo "<small style='color: white;'>还有{$diff->days}天</small>";
                                    } elseif ($diff->h > 0) {
                                        echo "<small style='color: #ffa726;'>还有{$diff->h}小时</small>";
                                    } else {
                                        echo "<small style='color: #ff6b6b;'>即将过期</small>";
                                    }
                                    ?>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($key['last_heartbeat']): ?>
                                    <div style="margin-bottom: 2px;"><?php echo format_time_ago($key['last_heartbeat']); ?></div>
                                    <?php if ($key['last_used_ip']): ?>
                                        <small style="opacity: 0.7;"><?php echo htmlspecialchars($key['last_used_ip']); ?></small>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span style="opacity: 0.5;">从未使用</span>
                                <?php endif; ?>
                            </td>
                            <td style="text-align: center;">
                                <div style="display: flex; gap: 4px; justify-content: center; flex-wrap: wrap;">
                                    <a href="index.php?page=keys&edit_key=<?php echo $key['id']; ?>" 
                                       class="btn btn-secondary glass-btn" 
                                       style="padding: 4px 8px; font-size: 11px;"
                                       title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    
                                    <?php if (!$is_expired): ?>
                                        <a href="index.php?page=keys&toggle_status=<?php echo $key['id']; ?>&status=<?php echo $key['status']; ?>" 
                                           class="btn <?php echo $key['status'] == 'active' ? 'btn-warning' : 'btn-success'; ?> glass-btn" 
                                           style="padding: 4px 8px; font-size: 11px;"
                                           title="<?php echo $key['status'] == 'active' ? '禁用' : '启用'; ?>"
                                           onclick="return confirm('确定要<?php echo $key['status'] == 'active' ? '禁用' : '启用'; ?>这个卡密吗？')">
                                            <i class="fas <?php echo $key['status'] == 'active' ? 'fa-ban' : 'fa-check'; ?>"></i>
                                        </a>
                                    <?php endif; ?>
                                    
                                    <a href="index.php?page=keys&delete_key=<?php echo $key['id']; ?>" 
                                       class="btn btn-danger glass-btn" 
                                       style="padding: 4px 8px; font-size: 11px;"
                                       title="删除"
                                       onclick="return confirm('确定要删除这个卡密吗？')">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <?php if ($total_pages > 1): ?>
            <div style="margin-top: 20px; text-align: center;">
                <div style="display: inline-flex; gap: 5px; align-items: center;">
                    <?php if ($page_num > 1): ?>
                        <a href="index.php?page=keys&p=<?php echo $page_num - 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>" class="btn btn-secondary">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    <?php endif; ?>
                    
                    <span style="color: rgba(255,255,255,0.8); margin: 0 15px;">
                        第 <?php echo $page_num; ?> 页，共 <?php echo $total_pages; ?> 页 (<?php echo $total_keys; ?> 条记录)
                    </span>
                    
                    <?php if ($page_num < $total_pages): ?>
                        <a href="index.php?page=keys&p=<?php echo $page_num + 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>" class="btn btn-secondary">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- 全局店铺详情弹窗容器 -->
<div id="global-stores-modal" class="stores-modal">
    <div class="stores-modal-content">
        <div class="stores-modal-header">
            <div class="stores-modal-title">
                <i class="fas fa-store"></i> 卡密绑定店铺 (<span id="stores-count">0</span>)
            </div>
            <button class="stores-modal-close" onclick="closeGlobalStoresModal()">&times;</button>
        </div>
        
        <div id="stores-container">
            <!-- 店铺内容将通过JavaScript动态添加 -->
        </div>
    </div>
</div>

<script>
// 权限卡片切换功能
function togglePermissionCard(cardElement, inputName) {
    const hiddenInput = document.getElementById(inputName);
    if (!hiddenInput) {
        console.error('找不到隐藏输入框:', inputName);
        return;
    }

    const isSelected = cardElement.classList.contains('selected');

    if (isSelected) {
        // 取消选中
        cardElement.classList.remove('selected');
        hiddenInput.value = '0';
    } else {
        // 选中
        cardElement.classList.add('selected');
        hiddenInput.value = '1';
    }

    // 【优化】处理店铺信息区域的必填状态
    handleStoreSection();

    console.log('权限卡片状态已更新:', inputName, '=', hiddenInput.value);
}

// 【优化】处理店铺信息区域的必填状态（微信店铺和抖店）
function handleStoreSection() {
    // 处理新增表单的微信店铺信息区域
    const wechatStoreSection = document.getElementById('wechat-store-section');
    const hasCustomerService = document.getElementById('has_customer_service');

    if (wechatStoreSection && hasCustomerService) {
        const isWechatSelected = hasCustomerService.value === '1';

        // 微信店铺信息区域始终显示，只改变必填状态
        const wechatInputs = wechatStoreSection.querySelectorAll('input[type="text"]');
        const wechatStoreNameRequired = document.getElementById('wechat-store-name-required');
        const wechatStoreIdRequired = document.getElementById('wechat-store-id-required');

        if (isWechatSelected) {
            // 设置为必填
            wechatInputs.forEach(input => {
                input.setAttribute('required', 'required');
                input.placeholder = input.placeholder.replace('（可选）', '');
            });
            // 显示必填标记
            if (wechatStoreNameRequired) wechatStoreNameRequired.style.display = 'inline';
            if (wechatStoreIdRequired) wechatStoreIdRequired.style.display = 'inline';
            console.log('新增表单：微信店铺字段设为必填');
        } else {
            // 设置为可选
            wechatInputs.forEach(input => {
                input.removeAttribute('required');
                if (!input.placeholder.includes('（可选）')) {
                    input.placeholder = input.placeholder + '（可选）';
                }
            });
            // 隐藏必填标记
            if (wechatStoreNameRequired) wechatStoreNameRequired.style.display = 'none';
            if (wechatStoreIdRequired) wechatStoreIdRequired.style.display = 'none';
            console.log('新增表单：微信店铺字段设为可选');
        }
    }

    // 处理新增表单的抖店信息区域
    const douyinStoreSection = document.getElementById('douyin-store-section');
    const hasProductListing = document.getElementById('has_product_listing');

    if (douyinStoreSection && hasProductListing) {
        const isDouyinSelected = hasProductListing.value === '1';

        // 抖店信息区域始终显示，只改变必填状态
        const douyinInputs = douyinStoreSection.querySelectorAll('input[type="text"]');
        const douyinStoreNameRequired = document.getElementById('douyin-store-name-required');
        const douyinStoreIdRequired = document.getElementById('douyin-store-id-required');

        if (isDouyinSelected) {
            // 设置为必填
            douyinInputs.forEach(input => {
                input.setAttribute('required', 'required');
                input.placeholder = input.placeholder.replace('（可选）', '');
            });
            // 显示必填标记
            if (douyinStoreNameRequired) douyinStoreNameRequired.style.display = 'inline';
            if (douyinStoreIdRequired) douyinStoreIdRequired.style.display = 'inline';
            console.log('新增表单：抖店字段设为必填');
        } else {
            // 设置为可选
            douyinInputs.forEach(input => {
                input.removeAttribute('required');
                if (!input.placeholder.includes('（可选）')) {
                    input.placeholder = input.placeholder + '（可选）';
                }
            });
            // 隐藏必填标记
            if (douyinStoreNameRequired) douyinStoreNameRequired.style.display = 'none';
            if (douyinStoreIdRequired) douyinStoreIdRequired.style.display = 'none';
            console.log('新增表单：抖店字段设为可选');
        }
    }

    // 处理编辑表单的微信店铺信息区域
    const editWechatStoreSection = document.getElementById('edit-wechat-store-section');
    const editHasCustomerService = document.getElementById('edit_has_customer_service');

    if (editWechatStoreSection && editHasCustomerService) {
        const isEditWechatSelected = editHasCustomerService.value === '1';

        // 微信店铺信息区域始终显示，只改变必填状态
        const editWechatInputs = editWechatStoreSection.querySelectorAll('input[type="text"]');
        const editWechatStoreNameRequired = document.getElementById('edit-wechat-store-name-required');
        const editWechatStoreIdRequired = document.getElementById('edit-wechat-store-id-required');

        if (isEditWechatSelected) {
            // 设置为必填
            editWechatInputs.forEach(input => {
                input.setAttribute('required', 'required');
                input.placeholder = input.placeholder.replace('（可选）', '');
            });
            // 显示必填标记
            if (editWechatStoreNameRequired) editWechatStoreNameRequired.style.display = 'inline';
            if (editWechatStoreIdRequired) editWechatStoreIdRequired.style.display = 'inline';
            console.log('编辑表单：微信店铺字段设为必填');
        } else {
            // 设置为可选
            editWechatInputs.forEach(input => {
                input.removeAttribute('required');
                if (!input.placeholder.includes('（可选）')) {
                    input.placeholder = input.placeholder + '（可选）';
                }
            });
            // 隐藏必填标记
            if (editWechatStoreNameRequired) editWechatStoreNameRequired.style.display = 'none';
            if (editWechatStoreIdRequired) editWechatStoreIdRequired.style.display = 'none';
            console.log('编辑表单：微信店铺字段设为可选');
        }
    }

    // 处理编辑表单的抖店信息区域
    const editDouyinStoreSection = document.getElementById('edit-douyin-store-section');
    const editHasProductListing = document.getElementById('edit_has_product_listing');

    if (editDouyinStoreSection && editHasProductListing) {
        const isEditDouyinSelected = editHasProductListing.value === '1';

        // 抖店信息区域始终显示，只改变必填状态
        const editDouyinInputs = editDouyinStoreSection.querySelectorAll('input[type="text"]');
        const editDouyinStoreNameRequired = document.getElementById('edit-douyin-store-name-required');
        const editDouyinStoreIdRequired = document.getElementById('edit-douyin-store-id-required');

        if (isEditDouyinSelected) {
            // 设置为必填
            editDouyinInputs.forEach(input => {
                input.setAttribute('required', 'required');
                input.placeholder = input.placeholder.replace('（可选）', '');
            });
            // 显示必填标记
            if (editDouyinStoreNameRequired) editDouyinStoreNameRequired.style.display = 'inline';
            if (editDouyinStoreIdRequired) editDouyinStoreIdRequired.style.display = 'inline';
            console.log('编辑表单：抖店字段设为必填');
        } else {
            // 设置为可选
            editDouyinInputs.forEach(input => {
                input.removeAttribute('required');
                if (!input.placeholder.includes('（可选）')) {
                    input.placeholder = input.placeholder + '（可选）';
                }
            });
            // 隐藏必填标记
            if (editDouyinStoreNameRequired) editDouyinStoreNameRequired.style.display = 'none';
            if (editDouyinStoreIdRequired) editDouyinStoreIdRequired.style.display = 'none';
            console.log('编辑表单：抖店字段设为可选');
        }
    }
}

// 表单提交验证
function validateKeyForm(form) {
    const hasCustomerService = document.getElementById('has_customer_service').value === '1';
    const hasProductListing = document.getElementById('has_product_listing').value === '1';

    if (!hasCustomerService && !hasProductListing) {
        alert('请至少选择一个卡密功能！');
        return false;
    }

    // 【优化】只有选择了微信小店功能时，才验证微信店铺信息
    if (hasCustomerService) {
        const wechatStoreName = document.querySelector('input[name="store_name"]');
        const wechatStoreId = document.querySelector('input[name="wechat_store_id"]');

        if (!wechatStoreName || !wechatStoreName.value.trim()) {
            alert('选择小梅花AI客服-微信小店功能时，微信店铺名称为必填项！');
            if (wechatStoreName) wechatStoreName.focus();
            return false;
        }

        if (!wechatStoreId || !wechatStoreId.value.trim()) {
            alert('选择小梅花AI客服-微信小店功能时，微信小店ID为必填项！');
            if (wechatStoreId) wechatStoreId.focus();
            return false;
        }
    }

    // 【优化】只有选择了抖店功能时，才验证抖店信息
    if (hasProductListing) {
        const douyinStoreName = document.querySelector('input[name="douyin_store_name"]');
        const douyinStoreId = document.querySelector('input[name="douyin_store_id"]');

        if (!douyinStoreName || !douyinStoreName.value.trim()) {
            alert('选择小梅花AI客服-抖店功能时，抖店店铺名称为必填项！');
            if (douyinStoreName) douyinStoreName.focus();
            return false;
        }

        if (!douyinStoreId || !douyinStoreId.value.trim()) {
            alert('选择小梅花AI客服-抖店功能时，抖店ID为必填项！');
            if (douyinStoreId) douyinStoreId.focus();
            return false;
        }
    }

    return true;
}

// 编辑表单验证
function validateEditKeyForm(form) {
    const hasCustomerService = document.getElementById('edit_has_customer_service').value === '1';
    const hasProductListing = document.getElementById('edit_has_product_listing').value === '1';

    if (!hasCustomerService && !hasProductListing) {
        alert('请至少选择一个卡密功能！');
        return false;
    }

    // 【优化】只有选择了微信小店功能时，才验证微信店铺信息
    if (hasCustomerService) {
        const wechatStoreName = document.querySelector('input[name="store_name"]');
        const wechatStoreId = document.querySelector('input[name="wechat_store_id"]');

        if (!wechatStoreName || !wechatStoreName.value.trim()) {
            alert('选择小梅花AI客服-微信小店功能时，微信店铺名称为必填项！');
            if (wechatStoreName) wechatStoreName.focus();
            return false;
        }

        if (!wechatStoreId || !wechatStoreId.value.trim()) {
            alert('选择小梅花AI客服-微信小店功能时，微信小店ID为必填项！');
            if (wechatStoreId) wechatStoreId.focus();
            return false;
        }
    }

    // 【优化】只有选择了抖店功能时，才验证抖店信息
    if (hasProductListing) {
        const douyinStoreName = document.querySelector('input[name="edit_douyin_store_name"]');
        const douyinStoreId = document.querySelector('input[name="edit_douyin_store_id"]');

        if (!douyinStoreName || !douyinStoreName.value.trim()) {
            alert('选择小梅花AI客服-抖店功能时，抖店店铺名称为必填项！');
            if (douyinStoreName) douyinStoreName.focus();
            return false;
        }

        if (!douyinStoreId || !douyinStoreId.value.trim()) {
            alert('选择小梅花AI客服-抖店功能时，抖店ID为必填项！');
            if (douyinStoreId) douyinStoreId.focus();
            return false;
        }
    }

    return true;
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('卡密管理页面权限卡片已初始化');

    // 【优化】初始化店铺信息区域的显示状态
    handleStoreSection();

    // 绑定表单验证
    const generateForm = document.querySelector('form[method="POST"]');
    if (generateForm && generateForm.querySelector('input[name="generate_key"]')) {
        generateForm.onsubmit = function() {
            return validateKeyForm(this);
        };
    }

    const editForm = document.querySelector('form[method="POST"]');
    if (editForm && editForm.querySelector('input[name="update_key"]')) {
        editForm.onsubmit = function() {
            return validateEditKeyForm(this);
        };
    }
});

function copyToClipboard() {
    const text = `<?php echo str_replace('<br>', '\n', $generated_key); ?>`;
    navigator.clipboard.writeText(text).then(() => {
        alert('卡密已复制到剪贴板！');
    }).catch(() => {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('卡密已复制到剪贴板！');
    });
}

// 添加店铺功能
document.addEventListener('DOMContentLoaded', function() {
    // 日期设置功能
    initializeDateSettings();

    // 新增卡密表单的添加店铺功能
    const addWechatStoreBtn = document.getElementById('add-wechat-store-btn');
    const addDouyinStoreBtn = document.getElementById('add-douyin-store-btn');
    const additionalStoresContainer = document.getElementById('additional-stores-container');
    let storeCount = 0;

    // 添加微信店铺
    if (addWechatStoreBtn) {
        addWechatStoreBtn.addEventListener('click', function() {
            storeCount++;
            const storeDiv = document.createElement('div');
            storeDiv.className = 'form-row additional-store';
            storeDiv.setAttribute('data-store-type', 'wechat');
            storeDiv.innerHTML = `
                <div class="form-group">
                    <label><i class="fas fa-store"></i> 额外微信店铺名称 #${storeCount}</label>
                    <input type="text" name="additional_store_names[]" placeholder="请输入微信店铺名称" required>
                </div>
                <div class="form-group">
                    <label><i class="fas fa-id-card"></i> 额外微信小店ID #${storeCount}</label>
                    <input type="text" name="additional_store_ids[]" placeholder="请输入微信小店ID" required>
                </div>
                <input type="hidden" name="additional_store_types[]" value="wechat">
                <button type="button" class="btn btn-danger remove-store-btn" style="height: 40px; margin-top: 28px;">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            additionalStoresContainer.appendChild(storeDiv);

            // 添加删除按钮事件
            const removeBtn = storeDiv.querySelector('.remove-store-btn');
            removeBtn.addEventListener('click', function() {
                storeDiv.remove();
            });
        });
    }

    // 添加抖店店铺
    if (addDouyinStoreBtn) {
        addDouyinStoreBtn.addEventListener('click', function() {
            storeCount++;
            const storeDiv = document.createElement('div');
            storeDiv.className = 'form-row additional-store';
            storeDiv.setAttribute('data-store-type', 'douyin');
            storeDiv.innerHTML = `
                <div class="form-group">
                    <label><i class="fas fa-store"></i> 额外抖店店铺名称 #${storeCount}</label>
                    <input type="text" name="additional_douyin_store_names[]" placeholder="请输入抖店店铺名称" required>
                </div>
                <div class="form-group">
                    <label><i class="fas fa-id-card"></i> 额外抖店ID #${storeCount}</label>
                    <input type="text" name="additional_douyin_store_ids[]" placeholder="请输入抖店ID" required>
                </div>
                <input type="hidden" name="additional_store_types[]" value="douyin">
                <button type="button" class="btn btn-danger remove-store-btn" style="height: 40px; margin-top: 28px;">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            additionalStoresContainer.appendChild(storeDiv);

            // 添加删除按钮事件
            const removeBtn = storeDiv.querySelector('.remove-store-btn');
            removeBtn.addEventListener('click', function() {
                storeDiv.remove();
            });
        });
    }
    
    // 编辑卡密表单的添加店铺功能
    const editAddWechatStoreBtn = document.getElementById('edit-add-wechat-store-btn');
    const editAddDouyinStoreBtn = document.getElementById('edit-add-douyin-store-btn');
    const editAdditionalStoresContainer = document.getElementById('edit-additional-stores-container');
    let editStoreCount = <?php echo count($additional_stores); ?>;

    // 编辑表单 - 添加微信店铺
    if (editAddWechatStoreBtn) {
        editAddWechatStoreBtn.addEventListener('click', function() {
            editStoreCount++;
            const storeDiv = document.createElement('div');
            storeDiv.className = 'form-inline additional-store';
            storeDiv.setAttribute('data-store-type', 'wechat');
            storeDiv.innerHTML = `
                <div class="form-group">
                    <label><i class="fas fa-store"></i> 额外微信店铺名称 #${editStoreCount}</label>
                    <input type="text" name="additional_store_names[]" placeholder="请输入微信店铺名称" required>
                </div>
                <div class="form-group">
                    <label><i class="fas fa-id-card"></i> 额外微信小店ID #${editStoreCount}</label>
                    <input type="text" name="additional_store_ids[]" placeholder="请输入微信小店ID" required>
                </div>
                <input type="hidden" name="additional_store_types[]" value="wechat">
                <button type="button" class="btn btn-danger remove-store-btn" style="height: 40px; margin-top: 28px;">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            editAdditionalStoresContainer.appendChild(storeDiv);

            // 添加删除按钮事件
            const removeBtn = storeDiv.querySelector('.remove-store-btn');
            removeBtn.addEventListener('click', function() {
                storeDiv.remove();
            });
        });
    }

    // 编辑表单 - 添加抖店店铺
    if (editAddDouyinStoreBtn) {
        editAddDouyinStoreBtn.addEventListener('click', function() {
            editStoreCount++;
            const storeDiv = document.createElement('div');
            storeDiv.className = 'form-inline additional-store';
            storeDiv.setAttribute('data-store-type', 'douyin');
            storeDiv.innerHTML = `
                <div class="form-group">
                    <label><i class="fas fa-store"></i> 额外抖店店铺名称 #${editStoreCount}</label>
                    <input type="text" name="additional_douyin_store_names[]" placeholder="请输入抖店店铺名称" required>
                </div>
                <div class="form-group">
                    <label><i class="fas fa-id-card"></i> 额外抖店ID #${editStoreCount}</label>
                    <input type="text" name="additional_douyin_store_ids[]" placeholder="请输入抖店ID" required>
                </div>
                <input type="hidden" name="additional_store_types[]" value="douyin">
                <button type="button" class="btn btn-danger remove-store-btn" style="height: 40px; margin-top: 28px;">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            editAdditionalStoresContainer.appendChild(storeDiv);

            // 添加删除按钮事件
            const removeBtn = storeDiv.querySelector('.remove-store-btn');
            removeBtn.addEventListener('click', function() {
                storeDiv.remove();
            });
        });
    }
    
    // 为已存在的删除按钮添加事件
    document.querySelectorAll('.remove-store-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            this.closest('.additional-store').remove();
        });
    });
});

// 显示全局店铺详情弹窗
function showStoresModal(keyId) {
    const dataContainer = document.querySelector(`.stores-data-container[data-key-id="${keyId}"]`);
    if (!dataContainer) return;
    
    const storeName = dataContainer.getAttribute('data-store-name');
    const storeId = dataContainer.getAttribute('data-store-id');
    const storesCount = dataContainer.getAttribute('data-stores-count');
    let additionalStores = [];
    
    try {
        const additionalStoresJson = dataContainer.getAttribute('data-additional-stores');
        if (additionalStoresJson) {
            additionalStores = JSON.parse(additionalStoresJson);
        }
    } catch (e) {
        console.error('解析额外店铺数据失败:', e);
    }
    
    // 更新弹窗内容
    const storesCountElement = document.getElementById('stores-count');
    storesCountElement.textContent = storesCount;
    
    // 清空并添加店铺内容
    const storesContainer = document.getElementById('stores-container');
    storesContainer.innerHTML = '';
    
    // 添加主店铺
    const mainStoreDiv = document.createElement('div');
    mainStoreDiv.className = 'store-item main-store';
    mainStoreDiv.innerHTML = `
        <div class="store-badge">主店铺</div>
        <div class="store-name">
            <i class="fas fa-store"></i> ${storeName || '未知店铺'}
        </div>
        <div class="store-id">
            <i class="fas fa-id-card"></i> 微信小店ID: ${storeId || '未知'}
        </div>
    `;
    storesContainer.appendChild(mainStoreDiv);
    
    // 添加额外店铺
    additionalStores.forEach((store, index) => {
        const storeDiv = document.createElement('div');
        storeDiv.className = 'store-item additional-store';
        storeDiv.innerHTML = `
            <div class="store-badge">关联店铺 #${index + 1}</div>
            <div class="store-name">
                <i class="fas fa-link"></i> ${store.store_name || '未知店铺'}
            </div>
            <div class="store-id">
                <i class="fas fa-id-card"></i> 微信小店ID: ${store.wechat_store_id || '未知'}
            </div>
        `;
        storesContainer.appendChild(storeDiv);
    });
    
    // 显示弹窗
    const modal = document.getElementById('global-stores-modal');
    modal.style.display = 'flex';
    modal.style.alignItems = 'center';
    modal.style.justifyContent = 'center';
    
    // 添加动画类
    setTimeout(() => {
        const content = modal.querySelector('.stores-modal-content');
        if (content) {
            content.classList.add('active');
        }
    }, 10);
}

// 关闭全局店铺详情弹窗
function closeGlobalStoresModal() {
    const modal = document.getElementById('global-stores-modal');
    if (!modal) return;

    // 添加关闭动画
    const content = modal.querySelector('.stores-modal-content');
    if (content) {
        content.classList.remove('active');
        content.classList.add('closing');

        // 等待动画完成后隐藏
        setTimeout(() => {
            modal.style.display = 'none';
            content.classList.remove('closing');
        }, 300);
    } else {
        modal.style.display = 'none';
    }
}

// 点击弹窗外部关闭
document.addEventListener('click', function(event) {
    const modal = document.getElementById('global-stores-modal');
    if (event.target === modal) {
        closeGlobalStoresModal();
    }
});

// 按ESC键关闭弹窗
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeGlobalStoresModal();
    }
});

// 日期设置功能初始化
function initializeDateSettings() {
    const upgradeOptions = document.querySelectorAll('input[name="upgrade_type"]');
    const manualDateInput = document.querySelector('input[name="manual_expiry_date"]');
    const datetimeInput = document.querySelector('input[name="expiry_date"]');

    if (!upgradeOptions.length || !manualDateInput || !datetimeInput) return;

    // 升级选项样式处理
    upgradeOptions.forEach(option => {
        const label = option.closest('.upgrade-option');

        option.addEventListener('change', function() {
            // 重置所有选项样式
            document.querySelectorAll('.upgrade-option').forEach(opt => {
                opt.style.background = 'rgba(255, 255, 255, 0.1)';
                opt.style.borderColor = 'transparent';
            });

            // 高亮选中的选项
            if (this.checked) {
                label.style.background = 'rgba(40, 167, 69, 0.3)';
                label.style.border = '1px solid rgba(40, 167, 69, 0.5)';

                // 清空手动输入
                manualDateInput.value = '';

                // 计算新的到期时间
                calculateUpgradeDate(this.value);
            }
        });

        // 悬停效果
        label.addEventListener('mouseenter', function() {
            if (!option.checked) {
                this.style.background = 'rgba(255, 255, 255, 0.2)';
            }
        });

        label.addEventListener('mouseleave', function() {
            if (!option.checked) {
                this.style.background = 'rgba(255, 255, 255, 0.1)';
            }
        });
    });

    // 手动输入日期处理
    manualDateInput.addEventListener('input', function() {
        const value = this.value.trim();
        if (value) {
            // 清除升级选项
            upgradeOptions.forEach(option => {
                option.checked = false;
                option.closest('.upgrade-option').style.background = 'rgba(255, 255, 255, 0.1)';
                option.closest('.upgrade-option').style.borderColor = 'transparent';
            });

            // 尝试解析手动输入的日期
            parseManualDate(value);
        }
    });

    // 日期选择器变化时清除其他选项
    datetimeInput.addEventListener('change', function() {
        if (this.value) {
            // 清除升级选项
            upgradeOptions.forEach(option => {
                option.checked = false;
                option.closest('.upgrade-option').style.background = 'rgba(255, 255, 255, 0.1)';
                option.closest('.upgrade-option').style.borderColor = 'transparent';
            });

            // 清空手动输入
            manualDateInput.value = '';
        }
    });
}

// 计算升级后的日期
function calculateUpgradeDate(upgradeType) {
    const currentExpiryInput = document.querySelector('input[name="expiry_date"]');
    if (!currentExpiryInput) return;

    const currentDate = new Date(currentExpiryInput.value);
    if (isNaN(currentDate.getTime())) return;

    let newDate = new Date(currentDate);

    switch (upgradeType) {
        case 'yearly':
            newDate.setFullYear(newDate.getFullYear() + 1);
            break;
        case 'half_yearly':
            newDate.setMonth(newDate.getMonth() + 6);
            break;
        case 'monthly':
            newDate.setMonth(newDate.getMonth() + 1);
            break;
        case 'daily':
            newDate.setDate(newDate.getDate() + 1);
            break;
    }

    // 更新日期选择器的值
    const formattedDate = newDate.getFullYear() + '-' +
                         String(newDate.getMonth() + 1).padStart(2, '0') + '-' +
                         String(newDate.getDate()).padStart(2, '0') + 'T' +
                         String(newDate.getHours()).padStart(2, '0') + ':' +
                         String(newDate.getMinutes()).padStart(2, '0');

    currentExpiryInput.value = formattedDate;
}

// 解析手动输入的日期格式
function parseManualDate(dateStr) {
    const datetimeInput = document.querySelector('input[name="expiry_date"]');
    if (!datetimeInput) return;

    // 支持格式: 2025-8-20-00:00
    const match = dateStr.match(/^(\d{4})-(\d{1,2})-(\d{1,2})-(\d{2}):(\d{2})$/);
    if (match) {
        const [, year, month, day, hour, minute] = match;

        // 验证日期有效性
        const date = new Date(year, month - 1, day, hour, minute);
        if (date.getFullYear() == year &&
            date.getMonth() == month - 1 &&
            date.getDate() == day) {

            // 格式化为datetime-local格式
            const formattedDate = year + '-' +
                                 String(month).padStart(2, '0') + '-' +
                                 String(day).padStart(2, '0') + 'T' +
                                 String(hour).padStart(2, '0') + ':' +
                                 String(minute).padStart(2, '0');

            datetimeInput.value = formattedDate;
        }
    }
}
</script>